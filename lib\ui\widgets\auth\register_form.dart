import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:tnvpn/ui/components/custom_button.dart';
import 'package:tnvpn/ui/components/custom_input.dart';

class RegisterForm extends StatelessWidget {
  final String name;
  final String email;
  final String phone;
  final String password;
  final String cpassword;
  final Function(String) onNameChanged;
  final Function(String) onEmailChanged;
  final Function(String) onPhoneChanged;
  final Function(String) onPasswordChanged;
  final Function(String) onCPasswordChanged;
  final VoidCallback onRegisterPressed;
  final VoidCallback onBackToLoginPressed;

  const RegisterForm({
    super.key,
    required this.name,
    required this.email,
    required this.phone,
    required this.password,
    required this.cpassword,
    required this.onNameChanged,
    required this.onEmailChanged,
    required this.onPhoneChanged,
    required this.onPasswordChanged,
    required this.onCPasswordChanged,
    required this.onRegisterPressed,
    required this.onBackToLoginPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 0, left: 20, right: 20),
          child: CustomInput(
            icon: Icons.person,
            initialValue: name,
            des: 'your_name'.tr(),
            onChange: onNameChanged,
            errorText: '',
            error: true,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 0, left: 20, right: 20),
          child: CustomInput(
            icon: Icons.email,
            initialValue: email,
            des: 'your_email'.tr(),
            onChange: onEmailChanged,
            errorText: '',
            error: true,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 0, left: 20, right: 20),
          child: CustomInput(
            icon: Icons.phone,
            initialValue: phone,
            des: 'your_phone'.tr(),
            onChange: onPhoneChanged,
            errorText: '',
            error: true,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 0, left: 20, right: 20),
          child: CustomInput(
            icon: Icons.lock,
            initialValue: password,
            des: 'password'.tr(),
            onChange: onPasswordChanged,
            pass: true,
            errorText: '',
            error: true,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 25, left: 20, right: 20),
          child: CustomInput(
            icon: Icons.lock,
            initialValue: cpassword,
            des: 'confirm_password'.tr(),
            onChange: onCPasswordChanged,
            pass: true,
            errorText: '',
            error: true,
          ),
        ),
        CustomButton(
          text: 'Register'.tr(),
          onPresse: onRegisterPressed,
          pading: const EdgeInsets.only(top: 20),
        ),
        const Padding(padding: EdgeInsets.only(top: 30)),
        TextButton(
          onPressed: onBackToLoginPressed,
          child: Text(
            "back_to_login".tr(),
            style: const TextStyle(
                fontSize: 14, color: Color.fromARGB(255, 105, 105, 105)),
          ),
        ),
      ],
    );
  }
}
