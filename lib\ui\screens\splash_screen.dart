import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tnvpn/core/https/servers_http.dart';
import 'package:tnvpn/core/providers/globals/data_provider.dart';
import 'package:tnvpn/core/resources/environment.dart';
import 'package:tnvpn/ui/components/custom_divider.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    DataProvider provider = context.read<DataProvider>();
    ServersHttp(context).allCountry().then((value) {
      provider.countries = value;
    });
    ServersHttp(context).package().then((value) {
      provider.packageConfig = value;
    });

    return Container(
      decoration: const BoxDecoration(color: Colors.white),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.transparent,
        body: Stack(
          fit: StackFit.expand,
          children: [
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset('assets/icons/logo.png',
                      width: MediaQuery.of(context).size.width * 0.35),
                  const ColumnDivider(space: 20),
                  Text(appName,
                      style: Theme.of(context).textTheme.titleLarge!.copyWith(
                          fontWeight: FontWeight.bold, color: Colors.black)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
