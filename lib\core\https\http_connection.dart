import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tnvpn/core/models/api_response.dart';
import 'package:tnvpn/core/resources/environment.dart';
import 'package:tnvpn/core/utils/utils.dart';

abstract class HttpConnection {
  final BuildContext context;

  HttpConnection(this.context);

  Future get<T>(String url,
      {Map<String, String>? params,
      Map<String, String>? headers,
      bool pure = false,
      bool printDebug = false}) async {
    headers ??= {"Accept": "application/json"};
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');
    headers.addAll({"Authorization": "Bearer $token"});
    try {
      Uri uri;
      if (pure) {
        uri = Uri.parse(url + (params != null ? paramsToString(params) : ''));
      } else {
        uri = Uri.parse(
            endpoint + url + (params != null ? paramsToString(params) : ''));
      }
      var response = await http.get(uri, headers: headers);
      if (pure) return response.body;
      if (response.statusCode == 200) {
        return ApiResponse<T>.fromJson(jsonDecode(response.body));
      } else {
        return ApiResponse<T>.fromJson({
          "status": false,
          "data": null,
          "message": "An error occurred, please try again later"
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      return ApiResponse<T>.fromJson({
        "status": false,
        "data": null,
        "message": "Failed to connect to server"
      });
    }
  }

  Future post<T>(String url,
      {Map<String, String>? params,
      Map<String, dynamic>? body,
      dynamic headers,
      bool pure = false,
      bool printDebug = false}) async {
    headers ??= {"Accept": "application/json"};
    body ??= {};
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');
    headers?.addAll({"Authorization": "Bearer $token"});
    try {
      var uri = Uri.parse(
          endpoint + url + (params != null ? paramsToString(params) : ''));
      var response = await http.post(uri, body: body, headers: headers);
      if (pure) return response.body;
      if (response.statusCode == 200 || response.statusCode == 422) {
        return ApiResponse<T>.fromJson(jsonDecode(response.body));
      }
      {
        return ApiResponse<T>.fromJson({
          "status": false,
          "data": null,
          "message": "An error occurred, please try again later"
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }

      return ApiResponse<T>.fromJson({
        "status": false,
        "data": null,
        "message": "Failed to connect to server"
      });
    }
  }

  Future put<T>(String url,
      {Map<String, String>? params,
      dynamic body,
      dynamic headers,
      bool pure = false,
      bool printDebug = false}) async {
    headers ??= {"Accept": "application/json"};
    body ??= {};
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');
    headers?.addAll({"Authorization": "Bearer $token"});
    try {
      var uri = Uri.parse(
          endpoint + url + (params != null ? paramsToString(params) : ''));
      var response = await http.put(uri, body: body, headers: headers);
      if (pure) return response.body;
      if (response.statusCode == 200 || response.statusCode == 422) {
        return ApiResponse<T>.fromJson(jsonDecode(response.body));
      } else {
        return ApiResponse<T>.fromJson({
          "status": false,
          "data": null,
          "message": "An error occurred, please try again later"
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      return ApiResponse<T>.fromJson({
        "status": false,
        "data": null,
        "message": "Failed to connect to server"
      });
    }
  }

  Future delete<T>(String url,
      {Map<String, String>? params,
      dynamic body,
      dynamic headers,
      bool pure = false,
      bool printDebug = false}) async {
    headers ??= {
      "Content-Type": "application/json",
      "Accept": "application/json"
    };
    body ??= {};
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');
    headers?.addAll({"Authorization": "Bearer $token"});
    try {
      var uri = Uri.parse(
          endpoint + url + (params != null ? paramsToString(params) : ''));
      var response = await http.delete(uri, body: body, headers: headers);
      if (pure) return response.body;
      if (response.statusCode == 200 || response.statusCode == 422) {
        return ApiResponse<T>.fromJson(jsonDecode(response.body));
      } else {
        return ApiResponse<T>.fromJson({
          "status": false,
          "data": null,
          "message": "An error occurred, please try again later"
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      return ApiResponse<T>.fromJson({
        "status": false,
        "data": null,
        "message": "Failed to connect to server"
      });
    }
  }
}
