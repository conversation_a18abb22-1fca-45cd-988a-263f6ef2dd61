import 'package:tnvpn/core/models/model.dart';

class VpnConfig extends Model {
  VpnConfig({
    required this.id,
    required this.name,
    required this.flag,
    required this.country,
    required this.ipAddress,
    this.type,
    this.username,
    this.password,
    this.port,
    this.protocol,
    this.config,
  });

  final String id;
  final String name;
  final String flag;
  final String country;
  final String ipAddress;
  final int? type;
  final String? username;
  final String? password;
  final String? port;
  final String? protocol;
  final String? config;

  factory VpnConfig.fromJson(Map<String, dynamic> json) => VpnConfig(
        id: json["id"].toString(),
        name: json["name"],
        flag: json["flag"],
        country: json["country"],
        ipAddress: json["ip_address"],
        type: json["type"],
        username: json["username"],
        password: json["password"],
        port: json["port"],
        protocol: json["protocol"],
        config: json["config"],
      );

  @override
  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "flag": flag,
        "country": country,
        "ip_address": ipAddress,
        "type": type,
        "username": username,
        "password": password,
        "port": port,
        "protocol": protocol,
        "config": config,
      };
}
