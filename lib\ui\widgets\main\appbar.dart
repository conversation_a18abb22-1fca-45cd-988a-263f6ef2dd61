import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_drawer/flutter_advanced_drawer.dart';
import 'package:tnvpn/core/resources/themes.dart';
import 'package:tnvpn/core/utils/utils.dart';
import 'package:tnvpn/ui/screens/my_ip_screen.dart';

class AppBarWidget extends StatelessWidget {
  final AdvancedDrawerController controller;

  const AppBarWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SizedBox(
        height: 60,
        child: Row(
          children: [
            IconButton(
                onPressed: () => controller.showDrawer(),
                icon: const Icon(Icons.menu)),
            const SizedBox(width: 8),
            Text("TN VPN",
                style: textTheme(context).titleLarge,
                textAlign: TextAlign.center),
            const Spacer(),
            TextButton(
              onPressed: () =>
                  startScreen(context, const ConnectionDetailScreen()),
              style: ButtonStyle(
                foregroundColor: MaterialStateProperty.resolveWith<Color>(
                  (Set<MaterialState> states) {
                    return Colors.black;
                  },
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('what_is_my_ip',
                          style: Theme.of(context).textTheme.bodySmall)
                      .tr(),
                  Icon(Icons.location_on,
                      color: Theme.of(context).dividerColor),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
