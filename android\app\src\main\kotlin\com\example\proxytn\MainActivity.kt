package com.tncompany.vpn

import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {

    private val CHANNEL = "ro-fe.com/native-code-example"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        MethodChannel(flutterEngine!!.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            if (call.method == "getMessageNativeCode") {
                val message = getMessageFromNativeCode()
                result.success(message)
            } else {
                result.notImplemented()
            }
        }
    }


    private fun getMessageFromNativeCode(): String {
        return "Hello from Kotlin native code!" 
    }
}
