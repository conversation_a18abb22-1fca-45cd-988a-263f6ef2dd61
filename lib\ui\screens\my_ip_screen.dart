import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:tnvpn/ui/widgets/my_ip/info_ip.dart';

class ConnectionDetailScreen extends StatelessWidget {
  const ConnectionDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: Text('what_is_my_ip'.tr()),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: ListView(
          children: const [
            InforIpWidget(),
          ],
        ),
      ),
    );
  }
}
