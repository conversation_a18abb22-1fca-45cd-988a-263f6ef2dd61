{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\Compressed\\flutter_windows_3.29.3-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\funny-proxy-client-mobile\\funny-proxy-client-mobile\\android\\app\\.cxx\\RelWithDebInfo\\6v4v23z5\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\Telegram Desktop\\funny-proxy-client-mobile\\funny-proxy-client-mobile\\android\\app\\.cxx\\RelWithDebInfo\\6v4v23z5\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}