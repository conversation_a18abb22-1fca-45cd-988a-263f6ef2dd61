import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tnvpn/core/https/auth_http.dart';
import 'package:tnvpn/core/providers/globals/data_provider.dart';
import 'package:tnvpn/core/providers/globals/theme_provider.dart';
import 'package:tnvpn/core/resources/environment.dart';
import 'package:tnvpn/core/utils/navigations.dart';
import 'package:tnvpn/ui/components/custom_divider.dart';
import 'package:tnvpn/ui/screens/auth_screen.dart';
import 'package:url_launcher/url_launcher.dart';

class MenuWidget extends StatelessWidget {
  const MenuWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text("user", style: Theme.of(context).textTheme.bodySmall).tr(),
          const Divider(thickness: 0.1),
          FutureBuilder<SharedPreferences>(
              future: SharedPreferences.getInstance(),
              builder: (context, snapshot) {
                var email =
                    snapshot.data?.getString('email') ?? 'No email found';
                return ListTile(
                  leading: const Icon(Icons.email),
                  title: Text(email),
                );
              }),
          Consumer<DataProvider>(
            builder: (context, provider, child) {
              return ListTile(
                leading: const Icon(Icons.calendar_today),
                title: Text(provider.packageConfig != null
                    ? provider.packageConfig!.package.name
                    : "Free"),
              );
            },
          ),
          ListTile(
              leading: const Icon(Icons.exit_to_app),
              title: const Text('logout').tr(),
              onTap: () => _logout(context)),
          const ColumnDivider(space: 20),
          Text("settings", style: Theme.of(context).textTheme.bodySmall).tr(),
          const Divider(thickness: 0.1),
          ListTile(
              leading: const Icon(Icons.color_lens),
              title: const Text('theme_mode').tr(),
              onTap: () =>
                  ThemeProvider.read(context).changeThemeMode(context)),
          ListTile(
              leading: const Icon(Icons.language),
              title: const Text('language').tr(),
              onTap: () => ThemeProvider.read(context).changeLanguage(context)),
          const ColumnDivider(space: 20),
          Text("about_us", style: Theme.of(context).textTheme.bodySmall).tr(),
          const Divider(thickness: 0.1),
          ListTile(
            leading: const Icon(Icons.privacy_tip),
            title: const Text('privacy_policy').tr(),
            onTap: () => _privacyPolicyClick(context),
          ),
          ListTile(
              leading: const Icon(Icons.description),
              title: const Text('terms_of_service').tr(),
              onTap: () => _teamServiceClick(context)),
          ListTile(
              leading: const Icon(Icons.description),
              title: const Text('vpn_policy').tr(),
              onTap: () => _vpnPolicyClick(context)),
          ListTile(
              leading: const Icon(Icons.contacts),
              title: const Text('contact').tr(),
              onTap: () => _contactClick(context)),
          FutureBuilder<PackageInfo>(
            future: PackageInfo.fromPlatform(),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                return ListTile(
                    leading: const Icon(Icons.info),
                    title: const Text('version')
                        .tr(args: [snapshot.data!.version]));
              } else {
                return const SizedBox.shrink();
              }
            },
          ),
        ],
      ),
    );
  }

  void _logout(BuildContext context) async {
    await AuthHttp(context).logout();
    startScreen(context, const LoginScreen());
  }

  void _privacyPolicyClick(BuildContext context) async {
    if (await canLaunch('${website}privacy')) {
      await launch('${website}privacy');
    }
  }

  void _teamServiceClick(BuildContext context) async {
    if (await canLaunch('${website}terms')) {
      await launch('${website}terms');
    }
  }

  void _vpnPolicyClick(BuildContext context) async {
    if (await canLaunch('${website}vpn-policy')) {
      await launch('${website}vpn-policy');
    }
  }

  void _contactClick(BuildContext context) async {
    if (await canLaunch(website)) {
      await launch(website);
    }
  }
}
