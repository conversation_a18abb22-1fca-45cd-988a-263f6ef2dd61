import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tnvpn/core/models/package_config.dart';

class DataProvider extends ChangeNotifier {
  List<String>? _countries;
  PackageConfig? _packageConfig;

  List<String>? get countries => _countries;

  set countries(List<String>? value) {
    _countries = value;
    notifyListeners();
  }

  PackageConfig? get packageConfig => _packageConfig;

  set packageConfig(PackageConfig? value) {
    _packageConfig = value;
    notifyListeners();
  }

  static DataProvider read(BuildContext context) => context.read();

  static DataProvider watch(BuildContext context) => context.watch();
}
