PODS:
  - app_tracking_transparency (0.0.1):
    - Flutter
  - country_codes (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - openvpn_flutter (0.0.1):
    - Flutter
  - OpenVPNAdapter (0.8.0):
    - OpenVPNAdapter/ASIO (= 0.8.0)
    - OpenVPNAdapter/LZ4 (= 0.8.0)
    - OpenVPNAdapter/mbedTLS (= 0.8.0)
    - OpenVPNAdapter/OpenVPN3 (= 0.8.0)
    - OpenVPNAdapter/OpenVPNAdapter (= 0.8.0)
    - OpenVPNAdapter/OpenVPNClient (= 0.8.0)
  - OpenVPNAdapter/ASIO (0.8.0)
  - OpenVPNAdapter/LZ4 (0.8.0)
  - OpenVPNAdapter/mbedTLS (0.8.0)
  - OpenVPNAdapter/OpenVPN3 (0.8.0)
  - OpenVPNAdapter/OpenVPNAdapter (0.8.0)
  - OpenVPNAdapter/OpenVPNClient (0.8.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - country_codes (from `.symlinks/plugins/country_codes/ios`)
  - Flutter (from `Flutter`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - openvpn_flutter (from `.symlinks/plugins/openvpn_flutter/ios`)
  - OpenVPNAdapter (from `https://github.com/ss-abramchuk/OpenVPNAdapter.git`, tag `0.8.0`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

EXTERNAL SOURCES:
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  country_codes:
    :path: ".symlinks/plugins/country_codes/ios"
  Flutter:
    :path: Flutter
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  openvpn_flutter:
    :path: ".symlinks/plugins/openvpn_flutter/ios"
  OpenVPNAdapter:
    :git: https://github.com/ss-abramchuk/OpenVPNAdapter.git
    :tag: 0.8.0
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

CHECKOUT OPTIONS:
  OpenVPNAdapter:
    :git: https://github.com/ss-abramchuk/OpenVPNAdapter.git
    :tag: 0.8.0

SPEC CHECKSUMS:
  app_tracking_transparency: e169b653478da7bb15a6c61209015378ca73e375
  country_codes: b0900f46ad686281d5dab438e354e44ad10f5941
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  openvpn_flutter: 35f2b149956e0be8d97c2af221321a495bcd92fa
  OpenVPNAdapter: 1d945f4528073ff641d4ed90d98e28f2b1c86d05
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  pointer_interceptor_ios: 508241697ff0947f853c061945a8b822463947c1
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 887c8f4db6befb267709d2088bdf9700da652e15

COCOAPODS: 1.16.2
