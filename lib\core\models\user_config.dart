import 'package:tnvpn/core/models/model.dart';

class UserConfig extends Model {
  final User user;
  final String typeToken;
  final String accessToken;

  UserConfig({
    required this.user,
    required this.typeToken,
    required this.accessToken,
  });

  factory UserConfig.fromJson(Map<String, dynamic> json) {
    return UserConfig(
      user: User.from<PERSON><PERSON>(json['user']),
      typeToken: json['type_token'],
      accessToken: json['access_token'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'type_token': typeToken,
      'access_token': accessToken,
    };
  }
}

class User {
  final String email;
  final dynamic phone;
  final String name;
  final int status;

  User({
    required this.email,
    required this.phone,
    required this.name,
    required this.status,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      email: json['email'],
      phone: json['phone'],
      name: json['name'],
      status: json['status'],
    );
  }

  Map<String, dynamic> to<PERSON>son() {
    return {
      'email': email,
      'phone': phone,
      'name': name,
      'status': status,
    };
  }
}
