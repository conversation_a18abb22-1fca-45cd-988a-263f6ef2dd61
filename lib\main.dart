import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:country_codes/country_codes.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tnvpn/core/providers/globals/data_provider.dart';
import 'package:tnvpn/core/providers/globals/theme_provider.dart';
import 'package:tnvpn/core/providers/main/vpn_provider.dart';
import 'package:tnvpn/core/resources/environment.dart';
import 'package:tnvpn/core/resources/themes.dart';
import 'package:tnvpn/root.dart';

main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Future.wait([
    CountryCodes.init(),
    EasyLocalization.ensureInitialized(),
    AppTrackingTransparency.requestTrackingAuthorization(),
  ].map((e) => Future.microtask(() => e)));

  return runApp(
    EasyLocalization(
      path: 'assets/languages',
      supportedLocales: supportedLocales,
      fallbackLocale: const Locale('en', 'US'),
      saveLocale: true,
      useOnlyLangCode: true,
      child: MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (context) => DataProvider()),
          ChangeNotifierProvider(create: (context) => ThemeProvider()),
          ChangeNotifierProvider(
              create: (context) => VpnProvider()..initialize()),
        ],
        builder: (context, child) => MaterialApp(
          themeMode: ThemeProvider.watch(context).themeMode,
          theme: lightTheme,
          darkTheme: darkTheme,
          supportedLocales: context.supportedLocales,
          locale: context.locale,
          localizationsDelegates: context.localizationDelegates,
          debugShowCheckedModeBanner: false,
          title: appName,
          home: const Root(),
        ),
      ),
    ),
  );
}
