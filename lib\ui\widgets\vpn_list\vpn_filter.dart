import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:multi_select_flutter/multi_select_flutter.dart';
import 'package:ndialog/ndialog.dart';
import 'package:provider/provider.dart';
import 'package:tnvpn/core/providers/globals/data_provider.dart';
import 'package:tnvpn/core/resources/environment.dart';

Future<Map<String, List<String>>> showFilterDialog(BuildContext context) async {
  DataProvider provider = context.read<DataProvider>();
  List<String>? country = provider.countries;
  List<MultiSelectItem<String>> itemCountry = [];
  List<MultiSelectItem<String>> itemType = [];
  for (String countryName in country ?? []) {
    MultiSelectItem<String> item =
        MultiSelectItem<String>(countryName, countryName);
    itemCountry.add(item);
  }
  MultiSelectItem<String> freeItem =
      MultiSelectItem<String>(typeVpn["Free"].toString(), "Free");
  MultiSelectItem<String> vipItem =
      MultiSelectItem<String>(typeVpn["Vip"].toString(), "Vip");
  itemType.add(freeItem);
  itemType.add(vipItem);

  List<String> selectedCountries = [];
  List<String> selectedTypes = [];

  bool? result = await NAlertDialog(
    title: Text("service_filter".tr()),
    blur: 10,
    content: Consumer(builder: (context, provider, child) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          MultiSelectDialogField(
            items: itemCountry,
            initialValue: const [],
            searchable: true,
            title: Text('Country'.tr()),
            buttonText: Text('Select country'.tr()),
            searchHint: 'search'.tr(),
            itemsTextStyle: Theme.of(context).textTheme.bodyLarge,
            selectedItemsTextStyle: Theme.of(context).textTheme.bodyLarge,
            confirmText: Text("Select".tr()),
            cancelText: Text("Cancel".tr()),
            dialogHeight: 200,
            onConfirm: (values) async {
              selectedCountries =
                  values.map((value) => value.toString()).toList();
            },
          ),
          MultiSelectDialogField(
            items: itemType,
            initialValue: const [],
            searchable: true,
            title: Text('Type'.tr()),
            buttonText: Text('Select type'.tr()),
            searchHint: 'search'.tr(),
            itemsTextStyle: Theme.of(context).textTheme.bodyLarge,
            selectedItemsTextStyle: Theme.of(context).textTheme.bodyLarge,
            confirmText: Text("Select".tr()),
            cancelText: Text("Cancel".tr()),
            dialogHeight: 125,
            onConfirm: (values) async {
              selectedTypes = values.map((value) => value.toString()).toList();
            },
          ),
        ],
      );
    }),
    actions: [
      TextButton(
        onPressed: () => Navigator.of(context).pop(false),
        child: Text("close".tr()),
      ),
      TextButton(
        onPressed: () => Navigator.of(context).pop(true),
        child: Text("search".tr()),
      ),
    ],
  ).show(context);

  if (result ?? false) {
    return {
      'selectedCountries': selectedCountries,
      'selectedTypes': selectedTypes,
    };
  } else {
    return {
      'selectedCountries': [],
      'selectedTypes': [],
    };
  }
}
