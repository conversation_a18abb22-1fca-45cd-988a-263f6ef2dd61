import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:tnvpn/ui/components/custom_button.dart';
import 'package:tnvpn/ui/components/custom_input.dart';

class LoginForm extends StatelessWidget {
  final String email;
  final String password;
  final Function(String) onEmailChanged;
  final Function(String) onPasswordChanged;
  final VoidCallback onLoginPressed;
  final VoidCallback onForgotPasswordPressed;
  final VoidCallback onRegisterPressed;

  const LoginForm({
    super.key,
    required this.email,
    required this.password,
    required this.onEmailChanged,
    required this.onPasswordChanged,
    required this.onLoginPressed,
    required this.onForgotPasswordPressed,
    required this.onRegisterPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 0, left: 20, right: 20),
          child: CustomInput(
            icon: Icons.email,
            initialValue: email,
            des: 'your_email'.tr(),
            onChange: onEmailChanged,
            errorText: '',
            error: true,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: CustomInput(
            icon: Icons.lock,
            initialValue: password,
            des: 'password'.tr(),
            onChange: onPasswordChanged,
            pass: true,
            errorText: '',
            error: true,
          ),
        ),
        Align(
          alignment: Alignment.centerRight,
          child: TextButton(
            style: ButtonStyle(
              padding: MaterialStateProperty.all<EdgeInsetsGeometry>(
                const EdgeInsets.only(right: 40.0, top: -10),
              ),
            ),
            onPressed: onForgotPasswordPressed,
            child: Text(
              "Forgot Password?".tr(),
              style: const TextStyle(
                  fontSize: 14, color: Color.fromARGB(255, 105, 105, 105)),
            ),
          ),
        ),
        CustomButton(
          text: 'login'.tr(),
          onPresse: onLoginPressed,
          pading: const EdgeInsets.only(top: 20),
        ),
        const Padding(padding: EdgeInsets.only(top: 30)),
        TextButton(
          onPressed: onRegisterPressed,
          child: Text(
            "Do not have an account? Register".tr(),
            style: const TextStyle(
                fontSize: 14, color: Color.fromARGB(255, 105, 105, 105)),
          ),
        ),
      ],
    );
  }
}
