import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:ndialog/ndialog.dart';

void showAlert(BuildContext context, String title, String message,
    {VoidCallback? onPressed}) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return NAlertDialog(
        blur: 10,
        title: Text(title).tr(),
        content: Text(message).tr(),
        actions: <Widget>[
          TextButton(
            onPressed: onPressed ??
                () {
                  Navigator.of(context).pop();
                },
            child: const Text('OK'),
          ),
        ],
      );
    },
  );
}
