import 'package:flutter/material.dart';
import 'package:tnvpn/core/resources/colors.dart';

class CustomButton extends StatefulWidget {
  final String text;
  final Function onPresse;
  final EdgeInsets pading;

  const CustomButton(
      {Key? key,
      required this.text,
      required this.onPresse,
      this.pading = const EdgeInsets.only(top: 0)})
      : super(key: key);

  @override
  _CustomButtonState createState() => _CustomButtonState();
}

class _CustomButtonState extends State<CustomButton> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.pading,
      child: ElevatedButton(
        onPressed: () {
          widget.onPresse();
        },
        style: ButtonStyle(
          padding: MaterialStateProperty.all(EdgeInsets.zero),
          backgroundColor: MaterialStateProperty.all<Color>(Colors.transparent),
          shadowColor: MaterialStateProperty.all<Color>(Colors.transparent),
          side: MaterialStateProperty.all<BorderSide>(BorderSide.none),
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(100.0),
            ),
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25.0),
              gradient: primaryGradient),
          child: Padding(
            padding:
                const EdgeInsets.only(left: 30, right: 30, bottom: 12, top: 12),
            child: Text(
              widget.text,
              style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white),
            ),
          ),
        ),
      ),
    );
  }
}
