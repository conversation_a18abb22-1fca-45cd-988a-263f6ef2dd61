import 'package:flutter/material.dart';
import 'package:openvpn_flutter/openvpn_flutter.dart';
import 'package:provider/provider.dart';
import 'package:tnvpn/core/models/vpn_config.dart';
import 'package:tnvpn/core/resources/environment.dart';

class VpnProvider extends ChangeNotifier {
  VPNStage? vpnStage;
  VpnStatus? vpnStatus;
  VpnConfig? _vpnConfig;

  VpnConfig? get vpnConfig => _vpnConfig;

  set vpnConfig(VpnConfig? value) {
    _vpnConfig = value;
    notifyListeners();
  }

  late OpenVPN engine;

  bool get isConnected => vpnStage == VPNStage.connected;

  void initialize() {
    engine = OpenVPN(
        onVpnStageChanged: onVpnStageChanged,
        onVpnStatusChanged: onVpnStatusChanged)
      ..initialize(
        lastStatus: onVpnStatusChanged,
        lastStage: (stage) => onVpnStageChanged(stage, stage.name),
        groupIdentifier: groupIdentifier,
        localizedDescription: localizationDescription,
        providerBundleIdentifier: providerBundleIdentifier,
      );
  }

  void onVpnStatusChanged(VpnStatus? status) {
    vpnStatus = status;
    notifyListeners();
  }

  void onVpnStageChanged(VPNStage stage, String rawStage) {
    vpnStage = stage;
    if (stage == VPNStage.error) {
      Future.delayed(const Duration(seconds: 3)).then((value) {
        vpnStage = VPNStage.disconnected;
      });
    }
    notifyListeners();
  }

  void connect() async {
    String? config;
    try {
      config = await OpenVPN.filteredConfig(vpnConfig?.config);
    } catch (e) {
      config = vpnConfig?.config;
    }
    if (config != null) {
      engine.connect(
        config,
        vpnConfig!.name,
        certIsRequired: true,
        username: vpnConfig!.username ?? "",
        password: vpnConfig!.password ?? "",
      );
    } else {
      // Handle the case where config is null, e.g., showing an error or fallback logic
      debugPrint("VPN configuration is invalid or missing.");
    }
  }

  void disconnect() {
    if (isConnected) {
      engine.disconnect();
    }
  }

  static VpnProvider watch(BuildContext context) => context.watch();

  static VpnProvider read(BuildContext context) => context.read();
}
