import 'package:dart_ping/dart_ping.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:ndialog/ndialog.dart';
import 'package:provider/provider.dart';
import 'package:tnvpn/core/https/servers_http.dart';
import 'package:tnvpn/core/models/vpn_config.dart';
import 'package:tnvpn/core/providers/main/vpn_provider.dart';
import 'package:tnvpn/core/resources/colors.dart';
import 'package:tnvpn/core/resources/environment.dart';
import 'package:tnvpn/core/utils/utils.dart';
import 'package:tnvpn/ui/components/custom_divider.dart';
import 'package:tnvpn/ui/components/custom_progess_dialog.dart';
import 'package:url_launcher/url_launcher.dart';

class VpnItem extends StatefulWidget {
  final VpnConfig config;

  const VpnItem(this.config, {super.key});

  @override
  State<VpnItem> createState() => _VpnItemState();
}

class _VpnItemState extends State<VpnItem> with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    DateTime now = DateTime.now();
    bool selected =
        context.watch<VpnProvider>().vpnConfig?.id == widget.config.id;
    super.build(context);
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: _itemClick,
      child: Container(
        padding: const EdgeInsets.all(10),
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        decoration: BoxDecoration(
          gradient: selected ? secondaryGradient : primaryGradient,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 32,
              height: 32,
              child: Image.asset("icons/flags/png/${widget.config.flag}.png",
                  package: "country_icons"),
            ),
            const RowDivider(),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(widget.config.name,
                          style: const TextStyle(color: Colors.white)),
                      const SizedBox(width: 5),
                      SizedBox(
                        width: 42,
                        height: 22,
                        child: TextButton(
                          onPressed: () {},
                          style: TextButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(
                                color: widget.config.type == typeVpn["Free"]
                                    ? const Color(0xFF1BC5BD)
                                    : const Color(0xFFFFA800),
                              ),
                            ),
                          ),
                          child: Text(
                            widget.config.type == typeVpn["Free"]
                                ? 'Free'
                                : 'Vip',
                            style: TextStyle(
                              color: widget.config.type == typeVpn["Free"]
                                  ? const Color(0xFF1BC5BD)
                                  : const Color(0xFFFFA800),
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 5),
                  const RowDivider(),
                ],
              ),
            ),
            const RowDivider(),
            FutureBuilder(
              future: Future.microtask(
                  () => Ping(widget.config.ipAddress, count: 1).stream.first),
              builder: (context, snapshot) {
                var ms = DateTime.now().difference(now).inMilliseconds;
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Image.asset("assets/icons/signal0.png",
                      width: 32, height: 32, color: Colors.grey.shade400);
                }
                if (ms < 80) {
                  return Image.asset("assets/icons/signal3.png",
                      width: 32, height: 32);
                } else if (ms < 150) {
                  return Image.asset("assets/icons/signal2.png",
                      width: 32, height: 32);
                } else if (ms < 300) {
                  return Image.asset("assets/icons/signal1.png",
                      width: 32, height: 32);
                } else if (ms > 300) {
                  return Image.asset("assets/icons/signal0.png",
                      width: 32, height: 32);
                }
                return Image.asset("assets/icons/signal0.png",
                    width: 32, height: 32, color: Colors.grey);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _itemClick() async {
    showCustomProgressDialog(context);

    ServersHttp(context).detailVpn(widget.config.id).then((value) {
      dismissCustomProgressDialog(context);
      if (value != null) {
        VpnProvider provider = context.read<VpnProvider>();
        provider.vpnConfig = value;
        provider.disconnect();
        closeScreen(context);
      } else {
        NAlertDialog(
          blur: 10,
          title: const Text("error").tr(),
          content: const Text("also_allowed_with_purchase").tr(),
          actions: [
            TextButton(
              child: const Text("close").tr(),
              onPressed: () async {
                Navigator.pop(context);
              },
            ),
            TextButton(
              child: const Text("view").tr(),
              onPressed: () async {
                Navigator.pop(context);
                if (await canLaunch('${website}shop')) {
                  await launch('${website}shop');
                }
              },
            ),
          ],
        ).show(context);
      }
    }).catchError((error) {
      Navigator.of(context).pop();
    });
  }

  @override
  bool get wantKeepAlive => true;
}
