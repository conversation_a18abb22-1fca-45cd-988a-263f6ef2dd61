import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_advanced_drawer/flutter_advanced_drawer.dart';
import 'package:tnvpn/ui/components/custom_divider.dart';
import 'package:tnvpn/ui/widgets/main/appbar.dart';
import 'package:tnvpn/ui/widgets/main/connection_button.dart';
import 'package:tnvpn/ui/widgets/main/menu.dart';
import 'package:tnvpn/ui/widgets/main/select_vpn.dart';
import 'package:tnvpn/ui/widgets/main/traffic.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  final AdvancedDrawerController _controller = AdvancedDrawerController();

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          SystemNavigator.pop();
          return false;
        },
        child: AdvancedDrawer(
          controller: _controller,
          backdropColor: Theme.of(context).scaffoldBackgroundColor,
          drawer: ListView(children: const [MenuWidget()]),
          child: Scaffold(
            resizeToAvoidBottomInset: false,
            body: Stack(
              fit: StackFit.expand,
              children: [
                ListView(
                  physics: const ClampingScrollPhysics(),
                  shrinkWrap: true,
                  children: [
                    AppBarWidget(controller: _controller),
                    SizedBox(
                      height: MediaQuery.of(context).size.height / 2,
                      child: const Align(
                        alignment: Alignment.center,
                        child: ConnectionButton(),
                      ),
                    ),
                    const TrafficWidget(),
                    const ColumnDivider(space: 20),
                    const SelectVpnWidget(),
                  ],
                ),
              ],
            ),
          ),
        ));
  }
}
