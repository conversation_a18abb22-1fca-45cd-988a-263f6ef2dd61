import 'package:shared_preferences/shared_preferences.dart';
import 'package:tnvpn/core/https/http_connection.dart';
import 'package:tnvpn/core/models/api_response.dart';
import 'package:tnvpn/core/models/user_config.dart';

class AuthHttp extends HttpConnection {
  AuthHttp(super.context);

  Future<Map<String, dynamic>> login(email, password) async {
    var params = {"email": email, "password": password};
    ApiResponse<Map<String, dynamic>> resp =
        await post<Map<String, dynamic>>("login", body: params);
    if (resp.status ?? false) {
      UserConfig user = UserConfig.fromJson(resp.data!);
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString('email', email);
      await prefs.setString('token', user.accessToken);

      return {"status": true};
    } else {
      return {
        "status": false,
        "message": resp.message ?? "Unknown error occurred"
      };
    }
  }

  Future logout() async {
    await get<Map<String, dynamic>>("logout");
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('email', '');
    await prefs.setString('token', '');
  }

  Future<Map<String, dynamic>> register(
      name, email, phone, password, passwordConfirmation) async {
    var params = {
      "name": name,
      "email": email,
      "phone": phone,
      "password": password,
      "password_confirmation": passwordConfirmation
    };
    ApiResponse<Map<String, dynamic>> resp =
        await post<Map<String, dynamic>>("register", body: params);
    if (resp.status ?? false) {
      return {"status": true, "message": resp.message};
    } else {
      return {
        "status": false,
        "message": resp.message ?? "Unknown error occurred"
      };
    }
  }

  Future<Map<String, dynamic>> forgotPassword(email) async {
    var params = {"email": email};
    ApiResponse<Map<String, dynamic>> resp =
        await post<Map<String, dynamic>>("forgot-pass", body: params);
    if (resp.status ?? false) {
      return {"status": true, "message": resp.message};
    } else {
      return {
        "status": false,
        "message": resp.message ?? "Unknown error occurred"
      };
    }
  }

  Future<Map<String, dynamic>> resetPassword(
      email, token, password, passwordConfirmation) async {
    var params = {
      "email": email,
      "token_reset": token,
      "password": password,
      "password_confirmation": passwordConfirmation
    };
    ApiResponse<Map<String, dynamic>> resp =
        await post<Map<String, dynamic>>("reset-pass", body: params);
    if (resp.status ?? false) {
      return {"status": true, "message": resp.message};
    } else {
      return {
        "status": false,
        "message": resp.message ?? "Unknown error occurred"
      };
    }
  }
}
