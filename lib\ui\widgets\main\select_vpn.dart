import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tnvpn/core/providers/main/vpn_provider.dart';
import 'package:tnvpn/core/utils/navigations.dart';
import 'package:tnvpn/ui/components/custom_divider.dart';
import 'package:tnvpn/ui/screens/vpn_list_screen.dart';

class SelectVpnWidget extends StatelessWidget {
  const SelectVpnWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text("select_vpn_server".tr(),
              style: Theme.of(context).textTheme.bodySmall),
          const ColumnDivider(space: 5),
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () => startScreen(context, const ServerListScreen()),
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Theme.of(context).scaffoldBackgroundColor,
                border: Border.all(color: Theme.of(context).dividerColor),
              ),
              child: Consumer<VpnProvider>(
                builder: (context, vpnProvider, child) {
                  var config = vpnProvider.vpnConfig;
                  return Row(
                    children: [
                      SizedBox(
                        width: 32,
                        height: 32,
                        child: config != null
                            ? Image.asset("icons/flags/png/${config.flag}.png",
                                package: "country_icons")
                            : Image.asset("assets/icons/smart_connect.png"),
                      ),
                      const SizedBox(width: 10),
                      Text(config != null ? config.name : 'smart_connect'.tr(),
                          style: Theme.of(context).textTheme.bodyLarge),
                      const Spacer(),
                      Icon(Icons.chevron_right,
                          color: Theme.of(context).dividerColor),
                      const SizedBox(width: 10),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
