import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:tnvpn/ui/components/custom_button.dart';
import 'package:tnvpn/ui/components/custom_input.dart';

class ResetPasswordForm extends StatelessWidget {
  final String token;
  final String password;
  final String cpassword;
  final Function(String) onTokenChanged;
  final Function(String) onPasswordChanged;
  final Function(String) onCPasswordChanged;
  final VoidCallback onResetPasswordPressed;
  final VoidCallback onBackToLoginPressed;

  const ResetPasswordForm({
    super.key,
    required this.token,
    required this.password,
    required this.cpassword,
    required this.onTokenChanged,
    required this.onPasswordChanged,
    required this.onCPasswordChanged,
    required this.onResetPasswordPressed,
    required this.onBackToLoginPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 0, left: 20, right: 20),
          child: CustomInput(
            icon: Icons.person,
            initialValue: token,
            des: 'OTP',
            onChange: onTokenChanged,
            errorText: '',
            error: true,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 0, left: 20, right: 20),
          child: CustomInput(
            icon: Icons.lock,
            initialValue: password,
            des: 'password'.tr(),
            onChange: onPasswordChanged,
            pass: true,
            errorText: '',
            error: true,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 25, left: 20, right: 20),
          child: CustomInput(
            icon: Icons.lock,
            initialValue: cpassword,
            des: 'confirm_password'.tr(),
            onChange: onCPasswordChanged,
            pass: true,
            errorText: '',
            error: true,
          ),
        ),
        CustomButton(
          text: 'ResetPassword'.tr(),
          onPresse: onResetPasswordPressed,
          pading: const EdgeInsets.only(top: 20),
        ),
        const Padding(padding: EdgeInsets.only(top: 30)),
        TextButton(
          onPressed: onBackToLoginPressed,
          child: Text(
            "back_to_login".tr(),
            style: const TextStyle(
                fontSize: 14, color: Color.fromARGB(255, 105, 105, 105)),
          ),
        ),
      ],
    );
  }
}
