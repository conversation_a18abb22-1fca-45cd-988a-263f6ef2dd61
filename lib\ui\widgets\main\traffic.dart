import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tnvpn/core/providers/main/vpn_provider.dart';
import 'package:tnvpn/core/utils/utils.dart';
import 'package:tnvpn/ui/components/custom_divider.dart';
import 'package:tnvpn/ui/screens/my_ip_screen.dart';

class TrafficWidget extends StatelessWidget {
  const TrafficWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text("traffic_usage".tr(),
              style: Theme.of(context).textTheme.bodySmall),
          const ColumnDivider(space: 5),
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () =>
                startScreen(context, const ConnectionDetailScreen()),
            child: Container(
              padding: EdgeInsets.zero,
              clipBehavior: Clip.antiAlias,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Theme.of(context).scaffoldBackgroundColor,
                border: Border.all(color: Theme.of(context).dividerColor),
              ),
              height: 91,
              child: Stack(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Consumer<VpnProvider>(
                            builder: (context, provider, child) {
                              double bytein = provider.isConnected
                                  ? doubleParse(
                                      provider.vpnStatus!.byteIn.toString())
                                  : 0;
                              double byteout = provider.isConnected
                                  ? doubleParse(
                                      provider.vpnStatus!.byteOut.toString())
                                  : 0;
                              return Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.stretch,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                                child: Text("download",
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodySmall)
                                                    .tr()),
                                            SizedBox(
                                              width: 30,
                                              height: 25,
                                              child: CircleAvatar(
                                                backgroundColor:
                                                    Theme.of(context)
                                                        .dividerColor,
                                                child: const Icon(
                                                    Icons
                                                        .arrow_downward_rounded,
                                                    size: 20,
                                                    color: Colors.white),
                                              ),
                                            ),
                                          ],
                                        ),
                                        Text(
                                            "${formatBytes(bytein.floor(), 0)}/s",
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyLarge),
                                      ],
                                    ),
                                  ),
                                  Container(
                                      height: 50,
                                      width: 1,
                                      color: Theme.of(context).dividerColor,
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 15)),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.stretch,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                                child: Text("upload",
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodySmall)
                                                    .tr()),
                                            SizedBox(
                                              width: 50,
                                              height: 25,
                                              child: CircleAvatar(
                                                backgroundColor:
                                                    Theme.of(context)
                                                        .dividerColor,
                                                child: const Icon(
                                                    Icons.arrow_upward_rounded,
                                                    size: 20,
                                                    color: Colors.white),
                                              ),
                                            ),
                                          ],
                                        ),
                                        Text(
                                            "${formatBytes(byteout.floor(), 0)}/s",
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyLarge),
                                      ],
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
