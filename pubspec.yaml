name: tnvpn
description: "TN Vpn is a mobile app to help hiding real IP"
publish_to: 'none' 

version: 1.0.0+1

environment:
  sdk: '>=3.2.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  app_tracking_transparency: ^2.0.4
  cached_network_image: ^3.3.0
  country_codes: ^3.1.0+2
  country_icons: ^3.0.0
  dart_ping: ^9.0.1
  dio: ^5.3.3
  easy_localization: ^3.0.3
  extended_tabs: ^4.1.0
  flutter_advanced_drawer: ^1.3.5
  flutter_html: ^3.0.0-alpha.6
  flutter_typeahead: ^5.0.1
  google_fonts: ^6.1.0
  http: ^1.1.0
  language_code: ^0.4.0
  lottie: ^3.0.0
  multi_select_flutter: ^4.1.3
  ndialog: ^4.3.1
  openvpn_flutter: ^1.2.2
  package_info_plus: ^5.0.1
  provider: ^6.0.5
  pull_to_refresh_flutter3: ^2.0.2
  shared_preferences: ^2.2.2
  shimmer: ^3.0.0
  url_launcher: ^6.3.1

  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_launcher_icons: ^0.14.2
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.1

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/icons/logo.png"
  remove_alpha_ios: true
  web:
    generate: false
  windows:
    generate: false
  macos:
    generate: false

flutter:
  uses-material-design: true
  assets:
    - "assets/"
    - "assets/images/"
    - "assets/icons/"
    - "assets/languages/"
