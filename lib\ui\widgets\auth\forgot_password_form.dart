import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:tnvpn/ui/components/custom_button.dart';
import 'package:tnvpn/ui/components/custom_input.dart';

class ForgotPasswordForm extends StatelessWidget {
  final String email;
  final Function(String) onEmailChanged;
  final VoidCallback onSubmitPressed;
  final VoidCallback onBackToLoginPressed;

  const ForgotPasswordForm({
    super.key,
    required this.email,
    required this.onEmailChanged,
    required this.onSubmitPressed,
    required this.onBackToLoginPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 0, left: 20, right: 20),
          child: CustomInput(
            icon: Icons.email,
            initialValue: email,
            des: 'your_email'.tr(),
            onChange: onEmailChanged,
            errorText: '',
            error: true,
          ),
        ),
        CustomButton(
          text: 'submit'.tr(),
          onPresse: onSubmitPressed,
          pading: const EdgeInsets.only(top: 20),
        ),
        const Padding(padding: EdgeInsets.only(top: 30)),
        TextButton(
          onPressed: onBackToLoginPressed,
          child: Text(
            "back_to_login".tr(),
            style: const TextStyle(
                fontSize: 14, color: Color.fromARGB(255, 105, 105, 105)),
          ),
        ),
      ],
    );
  }
}
