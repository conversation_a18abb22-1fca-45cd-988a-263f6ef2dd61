{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Downloads/Telegram Desktop/funny-proxy-client-mobile/funny-proxy-client-mobile/android/app/.cxx/RelWithDebInfo/6v4v23z5/x86_64", "source": "C:/Users/<USER>/Downloads/Compressed/flutter_windows_3.29.3-stable/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}