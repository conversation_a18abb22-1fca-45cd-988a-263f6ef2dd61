import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tnvpn/core/https/auth_http.dart';
import 'package:tnvpn/core/utils/navigations.dart';
import 'package:tnvpn/ui/components/custom_alert.dart';
import 'package:tnvpn/ui/screens/main_screen.dart';
import 'package:tnvpn/ui/widgets/auth/forgot_password_form.dart';
import 'package:tnvpn/ui/widgets/auth/login_form.dart';
import 'package:tnvpn/ui/widgets/auth/register_form.dart';
import 'package:tnvpn/ui/widgets/auth/reset_password_form.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool _isLoad = false;
  bool _isRegister = false;
  bool _isForgotPassword = false;
  bool _isResetPassword = false;
  String loginEmail = "";
  String loginPassword = "";
  String registerName = "";
  String registerEmail = "";
  String registerPhone = "";
  String registerPassword = "";
  String registerCpassword = "";
  String forgotEmail = "";
  String resetToken = "";
  String resetPassword = "";
  String resetCpassword = "";

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          SystemNavigator.pop();
          return false;
        },
        child: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: Scaffold(
            resizeToAvoidBottomInset: false,
            backgroundColor: Colors.white,
            body: Stack(
              fit: StackFit.expand,
              children: [
                SingleChildScrollView(
                  child: Center(
                    child: Column(
                      children: [
                        SizedBox(
                            height: MediaQuery.of(context).size.height * 0.05),
                        SizedBox(
                          height: MediaQuery.of(context).size.height * 0.3,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Image.asset(
                                'assets/images/logo.png',
                                width: MediaQuery.of(context).size.width * 0.35,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: MediaQuery.of(context).size.height * 0.6,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: _isRegister
                                ? _buildRegisterInputs(context)
                                : _isForgotPassword
                                    ? _buildForgotPasswordInputs(context)
                                    : _isResetPassword
                                        ? _buildResetPasswordInputs(context)
                                        : _buildLoginInputs(context),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                _isLoad
                    ? Positioned.fill(
                        child: Container(
                          height: 200,
                          color: Colors.black.withOpacity(0.1),
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                      )
                    : const SizedBox(),
              ],
            ),
          ),
        ));
  }

  List<Widget> _buildLoginInputs(BuildContext context) {
    return [
      LoginForm(
        email: loginEmail,
        password: loginPassword,
        onEmailChanged: (e) {
          setState(() {
            loginEmail = e;
          });
        },
        onPasswordChanged: (e) {
          setState(() {
            loginPassword = e;
          });
        },
        onLoginPressed: () => _loginClick(context),
        onForgotPasswordPressed: () {
          setState(() {
            _isForgotPassword = true;
            _isRegister = false;
          });
        },
        onRegisterPressed: () {
          setState(() {
            _isRegister = true;
            _isForgotPassword = false;
          });
        },
      ),
    ];
  }

  List<Widget> _buildForgotPasswordInputs(BuildContext context) {
    return [
      ForgotPasswordForm(
        email: forgotEmail,
        onEmailChanged: (e) {
          setState(() {
            forgotEmail = e;
          });
        },
        onSubmitPressed: () => _forgotPasswordClick(context),
        onBackToLoginPressed: () {
          setState(() {
            loginEmail = '';
            loginPassword = '';
            _isForgotPassword = false;
          });
        },
      ),
    ];
  }

  List<Widget> _buildResetPasswordInputs(BuildContext context) {
    return [
      ResetPasswordForm(
        token: resetToken,
        password: resetPassword,
        cpassword: resetCpassword,
        onTokenChanged: (e) {
          setState(() {
            resetToken = e;
          });
        },
        onPasswordChanged: (e) {
          setState(() {
            resetPassword = e;
          });
        },
        onCPasswordChanged: (e) {
          setState(() {
            resetCpassword = e;
          });
        },
        onResetPasswordPressed: () => _resetPasswordClick(context),
        onBackToLoginPressed: () {
          setState(() {
            loginEmail = '';
            loginPassword = '';
            _isResetPassword = false;
          });
        },
      ),
    ];
  }

  List<Widget> _buildRegisterInputs(BuildContext context) {
    return [
      RegisterForm(
        name: registerName,
        email: registerEmail,
        phone: registerPhone,
        password: registerPassword,
        cpassword: registerCpassword,
        onNameChanged: (e) {
          setState(() {
            registerName = e;
          });
        },
        onEmailChanged: (e) {
          setState(() {
            registerEmail = e;
          });
        },
        onPhoneChanged: (e) {
          setState(() {
            registerPhone = e;
          });
        },
        onPasswordChanged: (e) {
          setState(() {
            registerPassword = e;
          });
        },
        onCPasswordChanged: (e) {
          setState(() {
            registerCpassword = e;
          });
        },
        onRegisterPressed: () => _registerClick(context),
        onBackToLoginPressed: () {
          setState(() {
            loginEmail = '';
            loginPassword = '';
            _isRegister = false;
          });
        },
      ),
    ];
  }

  void _loginClick(BuildContext context) async {
    setState(() {
      _isLoad = true;
    });
    Map<String, dynamic> resp =
        await AuthHttp(context).login(loginEmail, loginPassword);
    setState(() {
      _isLoad = false;
    });
    if (resp["status"]) {
      startScreen(context, const MainScreen());
    } else {
      showAlert(context, "error", resp["message"]);
    }
  }

  void _forgotPasswordClick(BuildContext context) async {
    if (forgotEmail.isNotEmpty) {
      Map<String, dynamic> resp =
          await AuthHttp(context).forgotPassword(forgotEmail);
      if (resp["status"]) {
        void handleSuccessPressed(BuildContext context, Function setState) {
          Navigator.of(context).pop();
          setState(() {
            _isForgotPassword = false;
            _isResetPassword = true;
          });
        }

        showAlert(
          context,
          "success",
          resp["message"],
          onPressed: () => handleSuccessPressed(context, setState),
        );
      } else {
        showAlert(context, "error", resp["message"]);
      }
    }
  }

  void _resetPasswordClick(BuildContext context) async {
    setState(() {
      _isLoad = true;
    });
    Map<String, dynamic> resp = await AuthHttp(context)
        .resetPassword(forgotEmail, resetToken, resetPassword, resetCpassword);
    setState(() {
      _isLoad = false;
    });
    if (resp["status"]) {
      void handleSuccessPressed(BuildContext context, Function setState) {
        Navigator.of(context).pop();
        setState(() {
          _isResetPassword = false;
        });
      }

      showAlert(
        context,
        "success",
        resp["message"],
        onPressed: () => handleSuccessPressed(context, setState),
      );
    } else {
      showAlert(context, "error", resp["message"]);
    }
  }

  void _registerClick(BuildContext context) async {
    setState(() {
      _isLoad = true;
    });
    Map<String, dynamic> resp = await AuthHttp(context).register(registerName,
        registerEmail, registerPhone, registerPassword, registerCpassword);
    setState(() {
      _isLoad = false;
    });
    if (resp["status"]) {
      void handleSuccessPressed(BuildContext context, Function setState) {
        Navigator.of(context).pop();
        setState(() {
          _isRegister = false;
        });
      }

      showAlert(
        context,
        "success",
        resp["message"],
        onPressed: () => handleSuccessPressed(context, setState),
      );
    } else {
      showAlert(context, "error", resp["message"]);
    }
  }
}
