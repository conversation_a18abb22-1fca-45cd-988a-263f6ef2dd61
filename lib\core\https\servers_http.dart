import 'dart:convert';

import 'package:tnvpn/core/https/http_connection.dart';
import 'package:tnvpn/core/models/api_response.dart';
import 'package:tnvpn/core/models/ip.dart';
import 'package:tnvpn/core/models/package_config.dart';
import 'package:tnvpn/core/models/vpn_config.dart';

class ServersHttp extends HttpConnection {
  ServersHttp(super.context);

  Future<dynamic> servers(
      {String page = '1', List<String>? country, List<String>? type}) async {
    Map<String, String> filter = {"page": page};
    if (country != null && country.isNotEmpty) {
      for (int i = 0; i < country.length; i++) {
        String countryName = country[i];
        filter["country[$i]"] = countryName;
      }
    }
    if (type != null && type.isNotEmpty) {
      for (int i = 0; i < type.length; i++) {
        String typeName = type[i];
        filter["type[$i]"] = typeName;
      }
    }
    ApiResponse<List> resp = await get<List>("vpn/allservers", params: filter);
    if (resp.status ?? false) {
      return resp;
    }
    return null;
  }

  Future<PackageConfig?> package() async {
    ApiResponse<Map<String, dynamic>> resp =
        await get<Map<String, dynamic>>("vpn/package");
    if (resp.status ?? false) {
      return PackageConfig.fromJson(resp.data!);
    }
    return null;
  }

  Future<VpnConfig?> randomVpn() async {
    ApiResponse<Map<String, dynamic>> resp =
        await get<Map<String, dynamic>>("vpn/random");
    if (resp.status ?? false) {
      return VpnConfig.fromJson(resp.data!);
    }
    return null;
  }

  Future<VpnConfig?> detailVpn(String id) async {
    ApiResponse<Map<String, dynamic>> resp =
        await get<Map<String, dynamic>>("vpn/$id");
    if (resp.status ?? false) {
      return VpnConfig.fromJson(resp.data!);
    }
    return null;
  }

  Future<List<String>> allCountry() async {
    ApiResponse<Map<String, dynamic>> resp =
        await get<Map<String, dynamic>>("get-country");
    if (resp.status ?? false) {
      List<dynamic> countryListDynamic = resp.data!['country'];
      List<String> countryList =
          countryListDynamic.map((item) => item.toString()).toList();

      return countryList;
    }
    return [];
  }

  Future<Ip?> getPublicIP() async {
    var key = 'Sq4RvTUR41aO314';
    var resp = await get(
        "https://pro.ip-api.com/json/?key=$key&fields=20148211",
        pure: true);
    Map<String, dynamic> data = json.decode(resp);

    return resp != null ? Ip.fromJson(data) : null;
  }
}
