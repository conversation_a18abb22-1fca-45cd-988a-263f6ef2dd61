import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:openvpn_flutter/openvpn_flutter.dart';
import 'package:provider/provider.dart';
import 'package:tnvpn/core/https/servers_http.dart';
import 'package:tnvpn/core/providers/main/vpn_provider.dart';
import 'package:tnvpn/core/resources/colors.dart';

class ConnectionButton extends StatefulWidget {
  const ConnectionButton({super.key});

  @override
  State<ConnectionButton> createState() => _ConnectionButtonState();
}

class _ConnectionButtonState extends State<ConnectionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  Timer? interstitialTimeout;

  @override
  void initState() {
    _controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 1000));
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      loadInterstitial();
    });
    super.initState();
  }

  @override
  void dispose() {
    interstitialTimeout?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () => _connectButtonClick(context),
      child: SizedBox(
        height: 225,
        width: 225,
        child: Consumer<VpnProvider>(
          builder: (context, value, child) {
            switch (value.vpnStage ?? VPNStage.disconnected) {
              case VPNStage.connected:
                _controller.stop();
                return _connectedUI(context);
              case VPNStage.disconnected:
                _controller.stop();
                return _disconnectedUI(context);
              default:
                if (!_controller.isAnimating) {
                  _controller.repeat();
                }
                return _connectingUI(context, value.vpnStage!);
            }
          },
        ),
      ),
    );
  }

  Widget _connectedUI(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: primaryGradient,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(.4),
            blurRadius: 20,
            spreadRadius: 10,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      padding: const EdgeInsets.all(10),
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Theme.of(context).scaffoldBackgroundColor),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Icon(Icons.power_settings_new_rounded,
                size: 100, color: Theme.of(context).colorScheme.primary),
            Text(
              "DISCONNECTED".tr(),
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _connectingUI(BuildContext context, VPNStage stage) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: primaryGradient,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withOpacity(.4),
            blurRadius: 20,
            spreadRadius: 10,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      padding: const EdgeInsets.all(10),
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Theme.of(context).scaffoldBackgroundColor),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Icon(Icons.power_settings_new_rounded,
                size: 100, color: Theme.of(context).colorScheme.primary),
            Text(
              stage.name.replaceAll("_", " ").tr().toUpperCase(),
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _disconnectedUI(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: greyGradient,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(.4),
            blurRadius: 20,
            spreadRadius: 10,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      padding: const EdgeInsets.all(10),
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Theme.of(context).scaffoldBackgroundColor),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Icon(
              Icons.power_settings_new_rounded,
              size: 100,
              color: Colors.grey.withOpacity(.8),
            ),
            Text(
              "CONNECTED".tr(),
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  color: Colors.grey.withOpacity(.8),
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _connectButtonClick(BuildContext context) async {
    var vpnProvider = VpnProvider.read(context);
    vpnProvider.vpnConfig ??= await ServersHttp(context).randomVpn();
    if (vpnProvider.vpnStage != VPNStage.disconnected) {
      vpnProvider.disconnect();
    } else {
      vpnProvider.connect();
    }
  }

  void loadInterstitial() {
    interstitialTimeout?.cancel();
  }
}
