import 'package:tnvpn/core/models/model.dart';

class Ip extends Model {
  Ip({
    required this.asn,
    required this.city,
    required this.continent,
    required this.continentCode,
    required this.country,
    required this.countryCode,
    required this.isp,
    required this.lat,
    required this.lon,
    required this.organization,
    required this.query,
    required this.timezone,
    required this.zip,
  });

  String asn;
  String city;
  String continent;
  String continentCode;
  String country;
  String countryCode;
  String isp;
  double lat;
  double lon;
  String organization;
  String query;
  String timezone;
  String zip;

  factory Ip.fromJson(Map<String, dynamic> json) => Ip(
        asn: json["as"] ?? "",
        city: json["city"] ?? "",
        continent: json["continent"] ?? "",
        continentCode: json["continentCode"] ?? "",
        country: json["country"] ?? "",
        countryCode: json["countryCode"] ?? "",
        isp: json["isp"] ?? "",
        lat: (json["lat"] as num?)?.toDouble() ?? 0.0,
        lon: (json["lon"] as num?)?.toDouble() ?? 0.0,
        organization: json["org"] ?? "",
        query: json["query"] ?? "",
        timezone: json["timezone"] ?? "",
        zip: json["zip"] ?? "",
      );

  @override
  Map<String, dynamic> toJson() => {
        "as": asn,
        "city": city,
        "continent": continent,
        "continentCode": continentCode,
        "country": country,
        "countryCode": countryCode,
        "isp": isp,
        "lat": lat,
        "lon": lon,
        "org": organization,
        "query": query,
        "timezone": timezone,
        "zip": zip,
      };
}
