import 'package:tnvpn/core/models/model.dart';

class ApiResponse<T> extends Model {
  ApiResponse({
    this.status = false,
    this.message,
    this.data,
  });

  bool? status;
  String? message;
  T? data;

  factory ApiResponse.fromJson(Map<String, dynamic> json) => ApiResponse(
        status: json["status"],
        message: json["message"],
        data: json["data"],
      );

  @override
  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data,
      };
}
