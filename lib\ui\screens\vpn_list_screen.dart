import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tnvpn/core/https/servers_http.dart';
import 'package:tnvpn/core/models/vpn_config.dart';
import 'package:tnvpn/core/utils/utils.dart';
import 'package:tnvpn/ui/widgets/vpn_list/vpn_filter.dart';
import 'package:tnvpn/ui/widgets/vpn_list/vpn_item.dart';

class ServerListScreen extends StatefulWidget {
  const ServerListScreen({super.key});

  @override
  State<ServerListScreen> createState() => _ServerListScreenState();
}

class _ServerListScreenState extends State<ServerListScreen> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  List<VpnConfig> _servers = [];
  List<String>? country;
  List<String>? type;
  double page = 1;
  bool isLoading = true;

  @override
  void initState() {
    _servers = [];
    loadData();
    super.initState();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: const Text('server_list').tr(),
        actions: [
          IconButton(
            onPressed: () async {
              Map<String, List<String>> response =
                  await showFilterDialog(context);
              if (response['selectedCountries']!.isNotEmpty ||
                  response['selectedTypes']!.isNotEmpty) {
                setState(() {
                  _servers = [];
                  country = response['selectedCountries'];
                  type = response['selectedTypes'];
                  page = 1;
                  isLoading = true;
                });
                loadData();
              }
            },
            icon: const Icon(Icons.filter_alt, size: 35),
          ),
        ],
      ),
      body: SmartRefresher(
        onRefresh: loadData,
        controller: _refreshController,
        child: NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification scrollInfo) {
            if (scrollInfo is ScrollUpdateNotification &&
                scrollInfo.metrics.pixels ==
                    scrollInfo.metrics.maxScrollExtent) {
              loadData();
            }
            return true;
          },
          child: ListView(
            padding: const EdgeInsets.only(top: 5),
            children: [
              ...List.generate(
                _servers.length,
                (index) => VpnItem(_servers[index]),
              ),
              if (isLoading)
                Column(
                  children: [
                    Container(
                      height: 40,
                      alignment: Alignment.center,
                      child: const CircularProgressIndicator(),
                    ),
                    const SizedBox(height: 60),
                  ],
                )
              else
                const SizedBox(height: 60),
            ],
          ),
        ),
      ),
    );
  }

  void loadData() async {
    if (!isLoading) {
      return;
    }
    List<VpnConfig> resp = await ServersHttp(context)
        .servers(page: page.toString(), country: country, type: type)
        .then((value) {
      List<VpnConfig> data =
          value.data!.map<VpnConfig>((e) => VpnConfig.fromJson(e)).toList();
      page += 1;
      if (page >= doubleParse(value.message.toString())) {
        setState(() {
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = true;
        });
      }
      return data;
    });
    setState(() {
      _servers.addAll(resp);
    });
  }
}
