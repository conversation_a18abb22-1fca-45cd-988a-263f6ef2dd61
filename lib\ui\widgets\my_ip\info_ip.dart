import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:tnvpn/core/https/servers_http.dart';
import 'package:tnvpn/core/models/ip.dart';
import 'package:tnvpn/core/resources/themes.dart';
import 'package:tnvpn/ui/components/custom_divider.dart';

class InforIpWidget extends StatelessWidget {
  const InforIpWidget({super.key, this.callback});

  final Function(Ip value)? callback;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Ip?>(
      future: ServersHttp(context).getPublicIP(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return _loading();
        var ipDetail = snapshot.data;
        return Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                detail(
                  context,
                  "Ip Address".tr(),
                  ipDetail?.query ?? "N/A",
                  valueLead: Icon(Icons.network_wifi,
                      size: 20, color: textTheme(context).bodyLarge!.color),
                ),
                const ColumnDivider(space: 10),
                detail(
                  context,
                  'Country'.tr(),
                  ipDetail?.country ?? "N/A",
                  valueLead: Icon(Icons.language,
                      size: 20, color: textTheme(context).bodyLarge!.color),
                ),
                const ColumnDivider(space: 10),
                detail(
                  context,
                  'city'.tr(),
                  ipDetail?.city ?? "N/A",
                  valueLead: Icon(Icons.golf_course_outlined,
                      size: 20, color: textTheme(context).bodyLarge!.color),
                ),
                const ColumnDivider(space: 10),
                detail(
                  context,
                  "coordinates".tr(),
                  "${ipDetail?.lat.toString()}, ${ipDetail?.lon.toString()}",
                  valueLead: Icon(Icons.location_on,
                      size: 20, color: textTheme(context).bodyLarge!.color),
                ),
                const ColumnDivider(space: 10),
                detail(
                  context,
                  "timezone".tr(),
                  ipDetail?.timezone ?? "N/A",
                  valueLead: Icon(Icons.access_time_outlined,
                      size: 20, color: textTheme(context).bodyLarge!.color),
                ),
                const ColumnDivider(space: 10),
                detail(
                  context,
                  "Zip",
                  ipDetail?.zip ?? "N/A",
                  valueLead: Icon(Icons.code,
                      size: 20, color: textTheme(context).bodyLarge!.color),
                ),
                const ColumnDivider(space: 10),
                detail(
                  context,
                  "ISP",
                  ipDetail?.isp ?? "N/A",
                  valueLead: Icon(Icons.business,
                      size: 20, color: textTheme(context).bodyLarge!.color),
                ),
                const ColumnDivider(space: 10),
                detail(
                  context,
                  "organization".tr(),
                  ipDetail?.organization ?? "N/A",
                  valueLead: Icon(Icons.location_city,
                      size: 20, color: textTheme(context).bodyLarge!.color),
                ),
                const ColumnDivider(space: 10),
                detail(
                  context,
                  "AS",
                  ipDetail?.asn ?? "N/A",
                  valueLead: Icon(Icons.home,
                      size: 20, color: textTheme(context).bodyLarge!.color),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _loading() {
    return Column(
      children: [
        Container(
          height: 40,
          alignment: Alignment.center,
          child: const CircularProgressIndicator(),
        ),
        const SizedBox(
          height: 60,
        ),
      ],
    );
  }

  Widget detail(BuildContext context, String title, String value,
      {Widget? valueLead}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(title, style: textTheme(context).bodySmall),
        const ColumnDivider(space: 5),
        SizedBox(
          child: Row(
            children: [
              if (valueLead != null) ...[
                SizedBox(
                    width: 30,
                    child: CircleAvatar(
                        backgroundColor: Colors.grey.withOpacity(.1),
                        child: valueLead)),
                const RowDivider(space: 5),
              ],
              Expanded(
                  child: Text(value,
                      maxLines: 1, overflow: TextOverflow.ellipsis)),
            ],
          ),
        ),
      ],
    );
  }
}
