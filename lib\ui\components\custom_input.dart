import 'package:flutter/material.dart';

class CustomInput extends StatefulWidget {
  final IconData icon;
  final String des;
  final String initialValue;
  final Function onChange;
  final String errorText;
  final bool error;
  final bool pass;

  const CustomInput(
      {required this.icon,
      required this.des,
      required this.onChange,
      required this.errorText,
      required this.error,
      this.pass = false,
      this.initialValue = '',
      super.key});

  @override
  _CustomInputState createState() => _CustomInputState();
}

class _CustomInputState extends State<CustomInput> {
  FocusNode focusNode = FocusNode();
  final TextEditingController _textEditingController = TextEditingController();
  bool obscureText = true;

  @override
  void initState() {
    super.initState();
    obscureText = widget.pass;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TextField(
          style: const TextStyle(color: Colors.black),
          onChanged: (text) {
            widget.onChange(text);
          },
          controller: _textEditingController,
          obscureText: obscureText,
          decoration: InputDecoration(
            prefixIcon: Icon(
              widget.icon,
              color: const Color.fromARGB(115, 50, 50, 50),
            ),
            suffixIcon: widget.pass
                ? IconButton(
                    icon: Icon(
                      obscureText ? Icons.visibility : Icons.visibility_off,
                      color: const Color.fromARGB(115, 50, 50, 50),
                    ),
                    onPressed: () {
                      setState(() {
                        obscureText = !obscureText;
                      });
                    },
                  )
                : null,
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(30.0),
              borderSide: const BorderSide(
                color: Colors.green,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(30.0),
              borderSide: const BorderSide(
                color: Color.fromARGB(115, 50, 50, 50),
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(30.0),
              borderSide: const BorderSide(
                color: Colors.red,
              ),
            ),
            hintStyle: const TextStyle(
              color: Color.fromARGB(115, 50, 50, 50),
            ),
            hintText: widget.des,
            filled: true,
            fillColor: Colors.transparent,
          ),
          focusNode: focusNode,
        ),
        (widget.errorText != "")
            ? Container(
                width: double.infinity,
                margin: const EdgeInsets.only(left: 30, top: 1, bottom: 1),
                child: Text(
                  widget.errorText,
                  style: const TextStyle(color: Colors.red, fontSize: 16),
                ),
              )
            : const SizedBox(),
        !widget.pass
            ? Container(
                width: double.infinity,
                margin: const EdgeInsets.only(left: 30, top: 1, bottom: 1),
                child: const Text(
                  "",
                  style: TextStyle(color: Colors.red, fontSize: 16),
                ),
              )
            : const SizedBox(),
      ],
    );
  }
}
