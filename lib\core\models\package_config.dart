import 'package:tnvpn/core/models/model.dart';

class PackageConfig extends Model {
  final int id;
  final int packageId;
  final String expiredAt;
  final Package package;

  PackageConfig({
    required this.id,
    required this.packageId,
    required this.expiredAt,
    required this.package,
  });

  factory PackageConfig.fromJson(Map<String, dynamic> json) {
    return PackageConfig(
      id: json['id'],
      packageId: json['package_id'],
      expiredAt: json['expired_at'],
      package: Package.fromJson(json['package']),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'packageId': packageId,
      'expiredAt': expiredAt,
      'package': package.toJson(),
    };
  }
}

class Package {
  final int id;
  final String name;

  Package({
    required this.id,
    required this.name,
  });

  factory Package.fromJson(Map<String, dynamic> json) {
    return Package(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'id': id,
      'name': name,
    };
  }
}
