import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:tnvpn/core/https/servers_http.dart';
import 'package:tnvpn/core/utils/utils.dart';
import 'package:tnvpn/ui/screens/auth_screen.dart';
import 'package:tnvpn/ui/screens/main_screen.dart';
import 'package:tnvpn/ui/screens/splash_screen.dart';

class Root extends StatefulWidget {
  const Root({super.key});

  @override
  _RootState createState() => _RootState();
}

class _RootState extends State<Root> with WidgetsBindingObserver {
  bool _ready = false;

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      await Future.delayed(const Duration(seconds: 1));
      ServersHttp(context).randomVpn().then((value) {
        if (value == null) {
          startScreen(context, const LoginScreen());
        } else {
          startScreen(context, const MainScreen());
        }
      });

      if (!_ready) {
        setState(() {
          _ready = true;
        });
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: Theme.of(context).colorScheme.background,
      systemNavigationBarColor: Theme.of(context).colorScheme.background,
      statusBarIconBrightness: Theme.of(context).colorScheme.brightness,
      statusBarBrightness: Theme.of(context).colorScheme.brightness,
    ));
    return const SplashScreen();
  }
}
