import 'package:flutter/material.dart';

void showCustomProgressDialog(BuildContext context,
    {Color color = Colors.blue}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      );
    },
  );
}

void dismissCustomProgressDialog(BuildContext context) {
  Navigator.of(context, rootNavigator: true).pop();
}
