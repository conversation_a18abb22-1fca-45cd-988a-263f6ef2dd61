androidx.constraintlayout.widget.ConstraintLayout
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack
androidx.preference.SwitchPreference
io.flutter.view.TextureRegistry$ImageTextureEntry
androidx.core.widget.NestedScrollView
androidx.appcompat.widget.Toolbar
com.google.android.material.datepicker.MaterialCalendarGridView
androidx.core.app.CoreComponentFactory
kotlinx.coroutines.internal.StackTraceRecoveryKt
dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior
com.google.android.material.datepicker.MaterialTextInputPicker
com.google.android.material.button.MaterialButtonToggleGroup
com.google.android.material.floatingactionbutton.FloatingActionButton$BaseBehavior
com.google.android.gms.common.internal.ReflectedParcelable
com.jrai.flutter_keyboard_visibility.FlutterKeyboardVisibilityPlugin
androidx.appcompat.widget.ActionBarContextView
com.google.android.material.chip.Chip
androidx.annotation.Keep
com.google.android.gms.common.GooglePlayServicesManifestException
com.google.android.material.internal.NavigationMenuItemView
androidx.preference.CheckBoxPreference
com.google.android.gms.common.api.Scope
com.google.android.material.timepicker.TimePickerView
com.google.android.material.transformation.FabTransformationBehavior
androidx.preference.internal.PreferenceImageView
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
androidx.appcompat.widget.FitWindowsFrameLayout
io.flutter.plugins.GeneratedPluginRegistrant
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader
com.google.android.gms.common.annotation.KeepName
androidx.coordinatorlayout.widget.CoordinatorLayout
androidx.appcompat.view.menu.ListMenuItemView
com.google.android.material.internal.CheckableImageButton
androidx.preference.ListPreference
androidx.core.app.RemoteActionCompatParcelizer
androidx.preference.MultiSelectListPreference
com.google.android.material.timepicker.ChipTextInputComboView
com.google.android.material.snackbar.SnackbarContentLayout
androidx.appcompat.widget.ContentFrameLayout
androidx.appcompat.widget.DialogTitle
androidx.recyclerview.widget.LinearLayoutManager
com.google.android.material.internal.BaselineLayout
io.flutter.plugins.urllauncher.WebViewActivity
com.google.android.material.bottomappbar.BottomAppBar$Behavior
android.support.v4.graphics.drawable.IconCompatParcelizer
androidx.preference.TwoStatePreference
androidx.constraintlayout.helper.widget.Flow
id.laskarmedia.openvpn_flutter.OpenVPNFlutterPlugin
com.google.android.material.behavior.HideBottomViewOnScrollBehavior
androidx.preference.DialogPreference
androidx.appcompat.view.menu.ExpandedMenuView
com.google.android.material.transformation.FabTransformationSheetBehavior
com.google.android.material.timepicker.ClockFaceView
com.google.android.material.snackbar.BaseTransientBottomBar$Behavior
androidx.appcompat.widget.ButtonBarLayout
com.google.android.material.transformation.FabTransformationScrimBehavior
io.flutter.view.TextureRegistry$ImageConsumer
androidx.recyclerview.widget.GridLayoutManager
androidx.preference.DropDownPreference
io.flutter.view.AccessibilityViewEmbedder
com.google.android.material.search.SearchBar$ScrollingViewBehavior
de.blinkt.openvpn.core.NativeUtils
com.google.android.gms.auth.api.signin.GoogleSignInAccount
com.google.android.material.datepicker.MaterialDatePicker
androidx.fragment.app.DialogFragment
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer
io.flutter.plugins.pathprovider.PathProviderPlugin
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback
androidx.appcompat.widget.ActionBarOverlayLayout
com.google.android.gms.common.util.DynamiteApi
androidx.window.extensions.core.util.function.Consumer
androidx.appcompat.widget.ViewStubCompat
com.google.android.gms.common.GooglePlayServicesMissingManifestValueException
androidx.appcompat.widget.SearchView$SearchAutoComplete
androidx.recyclerview.widget.StaggeredGridLayoutManager
androidx.appcompat.widget.ActionBarContainer
com.google.android.material.appbar.MaterialToolbar
androidx.preference.SeekBarPreference
com.google.android.material.transformation.ExpandableBehavior
androidx.browser.browseractions.BrowserActionsFallbackMenuView
com.google.android.material.transformation.ExpandableTransformationBehavior
de.blinkt.openvpn.core.OpenVPNService
androidx.window.extensions.core.util.function.Function
androidx.appcompat.widget.SearchView
androidx.versionedparcelable.CustomVersionedParcelable
com.google.android.material.appbar.AppBarLayout$Behavior
com.google.android.gms.common.api.GoogleApiActivity
com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior
de.blinkt.openvpn.DisconnectVPNActivity
com.google.android.material.search.SearchView$Behavior
com.google.android.gms.common.api.Status
kotlin.coroutines.jvm.internal.BaseContinuationImpl
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper
androidx.preference.UnPressableLinearLayout
io.flutter.plugin.platform.SingleViewPresentation
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference
androidx.preference.PreferenceGroup
io.flutter.view.TextureRegistry$SurfaceProducer
androidx.preference.PreferenceCategory
io.flutter.view.FlutterCallbackInformation
androidx.fragment.app.FragmentContainerView
androidx.appcompat.widget.SwitchCompat
androidx.appcompat.widget.FitWindowsLinearLayout
androidx.appcompat.view.menu.ActionMenuItemView
com.tncompany.vpn.MainActivity
androidx.profileinstaller.ProfileInstallerInitializer
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
com.google.android.material.sidesheet.SideSheetBehavior
de.blinkt.openvpn.LaunchVPN
com.google.android.material.snackbar.Snackbar$SnackbarLayout
androidx.preference.SwitchPreferenceCompat
com.google.android.material.floatingactionbutton.FloatingActionButton$Behavior
de.blinkt.openvpn.VPNHelper
androidx.core.app.RemoteActionCompat
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback
com.google.android.material.button.MaterialButton
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
com.google.android.material.internal.NavigationMenuView
com.google.android.gms.common.SupportErrorDialogFragment
androidx.versionedparcelable.ParcelImpl
com.tekartik.sqflite.SqflitePlugin
io.flutter.plugins.urllauncher.UrlLauncherPlugin
androidx.preference.Preference
io.flutter.embedding.engine.FlutterJNI
com.google.android.material.internal.ClippableRoundedCornerLayout
com.google.android.material.timepicker.ClockHandView
androidx.preference.PreferenceScreen
com.google.android.gms.common.api.internal.LifecycleCallback
com.google.android.gms.common.api.internal.zzd
io.flutter.plugin.text.ProcessTextPlugin
androidx.core.graphics.drawable.IconCompat
com.miguelruivo.flutter.plugin.countrycodes.country_codes.CountryCodesPlugin
com.google.android.material.carousel.CarouselLayoutManager
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin
androidx.appcompat.widget.AlertDialogLayout
androidx.appcompat.widget.ActivityChooserView$InnerLayout
androidx.appcompat.app.AlertController$RecycleListView
io.flutter.view.TextureRegistry$GLTextureConsumer
androidx.preference.EditTextPreference
androidx.transition.FragmentTransitionSupport
androidx.profileinstaller.ProfileInstallReceiver
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
androidx.startup.InitializationProvider
androidx.window.extensions.core.util.function.Predicate
android.support.v4.app.RemoteActionCompatParcelizer
kotlinx.coroutines.android.AndroidDispatcherFactory
androidx.lifecycle.ProcessLifecycleInitializer
androidx.recyclerview.widget.RecyclerView
com.google.android.material.appbar.AppBarLayout$BaseBehavior
com.google.android.material.internal.TouchObserverFrameLayout
androidx.core.graphics.drawable.IconCompatParcelizer
com.google.android.material.behavior.SwipeDismissBehavior
com.google.android.material.textfield.TextInputLayout
com.google.android.material.datepicker.MaterialCalendar
androidx.appcompat.widget.ActionMenuView
androidx.emoji2.text.EmojiCompatInitializer
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback
com.google.android.material.textfield.TextInputEditText
com.google.android.gms.common.GooglePlayServicesIncorrectManifestValueException
io.flutter.view.TextureRegistry$SurfaceTextureEntry
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback
com.google.android.material.bottomsheet.BottomSheetBehavior
io.flutter.embedding.engine.FlutterOverlaySurface
com.google.android.gms.common.api.internal.BasePendingResult
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed
io.flutter.embedding.engine.FlutterJNI: float displayHeight
com.google.android.material.sidesheet.SideSheetBehavior$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.signin.internal.zag: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: java.lang.Runnable onFrameConsumed
kotlinx.coroutines.internal.AtomicOp: java.lang.Object _consensus
androidx.datastore.preferences.PreferencesProto$Value: java.lang.Object value_
io.flutter.embedding.engine.FlutterJNI: float displayWidth
io.flutter.embedding.engine.FlutterJNI: java.lang.String vmServiceUri
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackClassName
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.PreferencesProto$Value DEFAULT_INSTANCE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.Object lock
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean createNewReader
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated
io.flutter.view.AccessibilityViewEmbedder: java.util.Map originToFlutterId
io.flutter.embedding.engine.FlutterJNI: boolean initCalled
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
com.google.android.material.datepicker.CalendarConstraints: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: java.lang.Long nativeShellHolderId
com.google.android.gms.common.internal.RootTelemetryConfiguration: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder
com.google.android.gms.signin.internal.zaa: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate accessibilityDelegate
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback this$0
androidx.datastore.preferences.PreferencesProto$Value: int DOUBLE_FIELD_NUMBER
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl
androidx.fragment.app.FragmentState: android.os.Parcelable$Creator CREATOR
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
kotlinx.coroutines.android.HandlerContext: kotlinx.coroutines.android.HandlerContext _immediate
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix finalMatrix
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.MapFieldLite preferences_
io.flutter.embedding.engine.FlutterJNI: java.lang.String TAG
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.String TAG
io.flutter.plugin.platform.SingleViewPresentation: android.view.View$OnFocusChangeListener focusChangeListener
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState
io.flutter.embedding.engine.FlutterJNI: float displayDensity
io.flutter.view.AccessibilityViewEmbedder: io.flutter.view.AccessibilityViewEmbedder$ReflectionAccessors reflectionAccessors
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.HashMap perImageReaders
kotlinx.coroutines.flow.StateFlowSlot: java.lang.Object _state
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Internal$ProtobufList strings_
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage lastDequeuedImage
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.localization.LocalizationPlugin localizationPlugin
androidx.recyclerview.widget.StaggeredGridLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.CompletedExceptionally: int _handled
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.AccessibilityEventsDelegate accessibilityEventsDelegate
io.flutter.view.AccessibilityViewEmbedder: java.lang.String TAG
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastScheduleTime
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Parser PARSER
androidx.datastore.preferences.protobuf.GeneratedMessageLite: androidx.datastore.preferences.protobuf.UnknownFieldSetLite unknownFields
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean released
com.google.android.gms.common.internal.zzk: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.internal.ConnectionTelemetryConfiguration: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean ignoringFence
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.dart.PlatformMessageHandler platformMessageHandler
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.PreferencesProto$PreferenceMap DEFAULT_INSTANCE
com.google.android.gms.auth.api.signin.GoogleSignInAccount: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int memoizedSerializedSize
kotlinx.coroutines.DispatchedCoroutine: int _decision
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean trimOnMemoryPressure
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle lifecycle
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.Parser PARSER
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next
de.blinkt.openvpn.core.LogItem: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback animationCallback
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: java.lang.String TAG
io.flutter.embedding.engine.FlutterJNI: boolean loadLibraryCalled
io.flutter.plugin.platform.SingleViewPresentation: java.lang.String TAG
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate asyncWaitForVsyncDelegate
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean ignoringFence
de.blinkt.openvpn.core.TrafficHistory: android.os.Parcelable$Creator CREATOR
com.google.android.material.timepicker.TimeModel: android.os.Parcelable$Creator CREATOR
androidx.fragment.app.BackStackRecordState: android.os.Parcelable$Creator CREATOR
androidx.recyclerview.widget.LinearLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$PreferenceMap: int PREFERENCES_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastQueueTime
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedWidth
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
kotlinx.coroutines.sync.SemaphoreImpl: long enqIdx
io.flutter.embedding.engine.FlutterJNI: float refreshRateFPS
io.flutter.view.AccessibilityViewEmbedder: int nextFlutterId
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackLibraryPath
androidx.datastore.preferences.PreferencesProto$Value: int BOOLEAN_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state
com.google.android.material.bottomsheet.BottomSheetBehavior$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack
androidx.datastore.preferences.PreferencesProto$Value: int INTEGER_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev
androidx.datastore.preferences.PreferencesProto$Value: int STRING_FIELD_NUMBER
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List finalClippingPaths
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader: java.lang.ClassLoader sClassLoader
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask
com.google.android.gms.common.internal.MethodInvocation: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastDequeueTime
androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause
kotlinx.coroutines.internal.Segment: int cleanedAndPointers
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev
com.google.android.gms.common.internal.TelemetryData: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean CLEANUP_ON_MEMORY_PRESSURE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader lastReaderDequeuedFrom
io.flutter.embedding.engine.FlutterJNI: java.util.Set engineLifecycleListeners
kotlinx.coroutines.internal.ThreadSafeHeap: int _size
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState state
androidx.datastore.preferences.PreferencesProto$Value: int valueCase_
com.google.android.gms.common.ConnectionResult: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean released
androidx.datastore.preferences.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
com.google.android.material.datepicker.Month: android.os.Parcelable$Creator CREATOR
androidx.fragment.app.FragmentManagerState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.CancelledContinuation: int _resumed
io.flutter.view.AccessibilityViewEmbedder: java.util.Map embeddedViewToDisplayBounds
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause
io.flutter.embedding.engine.FlutterJNI: android.os.Looper mainLooper
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View view
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController platformViewsController
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
io.flutter.embedding.engine.FlutterJNI: java.util.concurrent.locks.ReentrantReadWriteLock shellHolderLock
io.flutter.plugin.platform.SingleViewPresentation: android.content.Context outerContext
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List mutators
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle
androidx.fragment.app.BackStackState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: boolean prefetchDefaultFontManagerCalled
io.flutter.view.AccessibilityViewEmbedder: android.util.SparseArray flutterIdToOrigin
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state
com.google.android.gms.common.api.Scope: android.os.Parcelable$Creator CREATOR
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackName
kotlinx.coroutines.channels.BufferedChannel: long receivers
io.flutter.embedding.engine.FlutterJNI: java.util.Set flutterUiDisplayListeners
com.google.android.gms.common.api.Status: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object head
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef
kotlinx.coroutines.EventLoopImplBase: int _isCompleted
kotlinx.coroutines.sync.SemaphoreImpl: long deqIdx
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation
kotlinx.coroutines.sync.SemaphoreImpl: int _availablePermits
io.flutter.plugin.platform.SingleViewPresentation: android.widget.FrameLayout container
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$AccessibilityDelegatingFrameLayout rootView
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int deferredInsetTypes
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
de.blinkt.openvpn.core.ConnectionStatus: android.os.Parcelable$Creator CREATOR
com.google.android.material.appbar.AppBarLayout$BaseBehavior$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image image
com.google.android.gms.common.Feature: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.JobSupport: java.lang.Object _state
com.google.android.gms.common.internal.GetServiceRequest: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean notifiedDestroy
androidx.recyclerview.widget.RecyclerView$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.common.internal.zat: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: int BYTES_FIELD_NUMBER
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: int STRING_SET_FIELD_NUMBER
androidx.appcompat.widget.SearchView$SavedState: android.os.Parcelable$Creator CREATOR
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean VERBOSE_LOGS
com.google.android.gms.signin.internal.zai: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler
com.google.android.gms.common.internal.zav: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean attached
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayDeque imageReaderQueue
androidx.datastore.preferences.PreferencesProto$StringSet: int STRINGS_FIELD_NUMBER
androidx.transition.ChangeBounds$6: androidx.transition.ChangeBounds$ViewBounds mViewBounds
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$InsetsListener insetsListener
com.google.android.gms.signin.internal.zak: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment
androidx.datastore.preferences.PreferencesProto$Value: int FLOAT_FIELD_NUMBER
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_IMAGES
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface surface
de.blinkt.openvpn.core.TrafficHistory$TrafficDatapoint: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback callback
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
com.google.android.material.textfield.TextInputLayout$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.InvokeOnCancelling: int _invoked
androidx.coordinatorlayout.widget.CoordinatorLayout$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.material.button.MaterialButton$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterOverlaySurface: int id
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object tail
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean newFrameAvailable
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.protobuf.Parser PARSER
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean needsSave
androidx.datastore.preferences.PreferencesProto$Value: int LONG_FIELD_NUMBER
androidx.datastore.preferences.PreferencesProto$Value: int bitField0_
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.PreferencesProto$StringSet DEFAULT_INSTANCE
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean released
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture
io.flutter.plugin.platform.SingleViewPresentation: int viewId
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner
com.google.android.material.datepicker.DateValidatorPointForward: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets lastWindowInsets
kotlinx.coroutines.EventLoopImplBase$DelayedTask: java.lang.Object _heap
androidx.datastore.preferences.protobuf.AbstractMessageLite: int memoizedHashCode
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedHeight
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager deferredComponentManager
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean animating
io.flutter.plugins.GeneratedPluginRegistrant: java.lang.String TAG
io.flutter.plugin.platform.SingleViewPresentation: boolean startFocused
androidx.fragment.app.FragmentManager$LaunchedFragmentInfo: android.os.Parcelable$Creator CREATOR
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
com.google.android.material.internal.CheckableImageButton$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.DefaultExecutor: int debugStatus
io.flutter.view.AccessibilityViewEmbedder: android.view.View rootAccessibilityView
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next
io.flutter.embedding.engine.FlutterJNI: void removeIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.constraintlayout.widget.ConstraintLayout: void setConstraintSet(androidx.constraintlayout.widget.ConstraintSet)
androidx.appcompat.widget.SwitchCompat: void setSplitTrack(boolean)
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsRegionalIndicator(int)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
io.flutter.view.TextureRegistry$SurfaceProducer: boolean handlesCropAndRotation()
io.flutter.embedding.engine.FlutterJNI: void scheduleFrame()
com.google.android.material.button.MaterialButton: void setStrokeWidth(int)
androidx.appcompat.widget.SwitchCompat: void setTextOff(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void prefetchDefaultFontManager()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: float getActionTextColorAlpha()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
com.google.android.gms.common.api.internal.zzd: zzd()
com.google.android.material.textfield.TextInputLayout: int getBoxCollapsedPaddingTop()
androidx.recyclerview.widget.RecyclerView: int getMaxFlingVelocity()
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: android.util.DisplayMetrics getWindowAreaDisplayMetrics()
androidx.core.widget.TextViewCompat$Api24Impl: android.icu.text.DecimalFormatSymbols getInstance(java.util.Locale)
com.google.android.gms.common.ErrorDialogFragment: ErrorDialogFragment()
com.google.android.material.chip.Chip: float getCloseIconEndPadding()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: androidx.window.extensions.area.ExtensionWindowAreaPresentation getRearDisplayPresentation()
androidx.constraintlayout.widget.ConstraintLayout: void setMaxWidth(int)
com.google.android.material.textfield.TextInputLayout: int getHelperTextCurrentTextColor()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void release()
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportButtonTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.button.MaterialButton: void setStrokeColor(android.content.res.ColorStateList)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void startRearDisplaySession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
androidx.constraintlayout.helper.widget.Flow: void setWrapMode(int)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterTexture(long,long,java.lang.ref.WeakReference)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
io.flutter.embedding.android.FlutterView: io.flutter.plugin.common.BinaryMessenger getBinaryMessenger()
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getStartIconDrawable()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
com.google.android.material.internal.NavigationMenuItemView: void setIconSize(int)
com.google.android.material.behavior.HideBottomViewOnScrollBehavior: HideBottomViewOnScrollBehavior(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void onDisplayOverlaySurface(int,int,int,int,int)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder createBuilder(android.content.Context,java.lang.String)
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: float getTextStartPadding()
com.google.android.material.search.SearchBar$ScrollingViewBehavior: SearchBar$ScrollingViewBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: void setChipText(java.lang.CharSequence)
com.google.android.material.internal.ForegroundLinearLayout: void setForegroundGravity(int)
com.google.android.material.chip.Chip: float getChipMinHeight()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspotBounds(android.graphics.drawable.Drawable,int,int,int,int)
com.google.android.material.drawable.DrawableUtils$OutlineCompatR: void setPath(android.graphics.Outline,android.graphics.Path)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax[] values()
androidx.appcompat.widget.SearchView$Api29Impl: void refreshAutoCompleteResults(android.widget.AutoCompleteTextView)
com.google.android.material.button.MaterialButton: int getIconGravity()
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
com.google.android.material.textfield.TextInputLayout: android.widget.ImageView$ScaleType getStartIconScaleType()
com.google.android.material.chip.Chip: void setIconEndPadding(float)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
com.google.android.material.chip.Chip: void setChipCornerRadiusResource(int)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getContentDescription(android.view.MenuItem)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.appcompat.view.ContextThemeWrapper$Api17Impl: android.content.Context createConfigurationContext(androidx.appcompat.view.ContextThemeWrapper,android.content.res.Configuration)
androidx.appcompat.resources.Compatibility$Api21Impl: int getChangingConfigurations(android.content.res.TypedArray)
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setCategory(android.app.Notification$Builder,java.lang.String)
androidx.core.app.AppOpsManagerCompat$Api29Impl: android.app.AppOpsManager getSystemService(android.content.Context)
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.android.FlutterImageView getCurrentImageSurface()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader()
androidx.appcompat.widget.SearchView: void setOnSuggestionListener(androidx.appcompat.widget.SearchView$OnSuggestionListener)
androidx.coordinatorlayout.widget.CoordinatorLayout: androidx.core.view.WindowInsetsCompat getLastWindowInsets()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
com.google.android.material.appbar.MaterialToolbar: void setLogoAdjustViewBounds(boolean)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection valueOf(java.lang.String)
androidx.constraintlayout.solver.widgets.analyzer.DependencyNode$Type: androidx.constraintlayout.solver.widgets.analyzer.DependencyNode$Type[] values()
com.google.android.material.textfield.TextInputLayout: void setStartIconOnLongClickListener(android.view.View$OnLongClickListener)
com.google.android.material.textfield.TextInputLayout: void setCursorColor(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
androidx.transition.ViewUtilsApi22$Api29Impl: void setLeftTopRightBottom(android.view.View,int,int,int,int)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getCursorColor()
androidx.core.view.ViewCompat$Api26Impl: boolean isImportantForAutofill(android.view.View)
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
androidx.appcompat.widget.AppCompatButton: int getAutoSizeMaxTextSize()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.preference.internal.PreferenceImageView: PreferenceImageView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void maybeWaitOnFence(android.media.Image)
androidx.appcompat.widget.AppCompatImageView: void setImageLevel(int)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api30Impl: void setImportantForContentCapture(android.view.View,int)
de.blinkt.openvpn.core.NativeUtils: java.lang.String getJNIAPI()
androidx.core.view.ViewCompat$Api29Impl: java.util.List getSystemGestureExclusionRects(android.view.View)
com.google.android.material.chip.Chip: void setCheckedIconEnabled(boolean)
androidx.constraintlayout.helper.widget.Flow: void setVerticalStyle(int)
com.google.android.material.textfield.TextInputLayout: void setHint(java.lang.CharSequence)
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.constraintlayout.widget.Guideline: void setGuidelineEnd(int)
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
io.flutter.embedding.engine.FlutterJNI: float getScaledFontSize(float,int)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.AccessibilityEventsDelegate,io.flutter.plugin.platform.SingleViewPresentation$PresentationState,android.view.View$OnFocusChangeListener,boolean)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
com.google.android.material.button.MaterialButton: void setIcon(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
io.flutter.embedding.engine.FlutterJNI: void nativeMarkTextureFrameAvailable(long,long)
androidx.recyclerview.widget.GridLayoutManager: GridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.recyclerview.widget.RecyclerView: boolean getPreserveFocusAfterLayout()
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportButtonTintList()
androidx.appcompat.widget.SearchView: void setInputType(int)
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: void setBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: void setChipMinHeight(float)
com.google.android.material.button.MaterialButton: android.text.Layout$Alignment getActualTextAlignment()
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
androidx.appcompat.widget.SearchView: int getSuggestionRowLayout()
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: void detachFromNativeAndReleaseResources()
androidx.constraintlayout.helper.widget.Flow: void setFirstHorizontalStyle(int)
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: boolean isHorizontallyScrollable(android.widget.TextView)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onImage(android.media.ImageReader,android.media.Image)
com.google.android.material.button.MaterialButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: void setEndIconCheckable(boolean)
androidx.constraintlayout.widget.ConstraintAttribute$AttributeType: androidx.constraintlayout.widget.ConstraintAttribute$AttributeType[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterImageTexture(long,long,java.lang.ref.WeakReference)
androidx.preference.DropDownPreference: DropDownPreference(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
io.flutter.embedding.engine.FlutterJNI: void ensureAttachedToNative()
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnTrimMemoryListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.core.app.NotificationCompatBuilder$Api29Impl: android.app.Notification$Builder setBubbleMetadata(android.app.Notification$Builder,android.app.Notification$BubbleMetadata)
com.google.android.material.button.MaterialButton: void setCornerRadius(int)
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
com.google.android.material.transformation.FabTransformationScrimBehavior: FabTransformationScrimBehavior()
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getPlaceholderTextColor()
androidx.core.view.ViewCompat$Api30Impl: int getImportantForContentCapture(android.view.View)
androidx.core.view.ViewCompat$Api26Impl: boolean isFocusedByDefault(android.view.View)
androidx.appcompat.widget.SearchView: void setOnCloseListener(androidx.appcompat.widget.SearchView$OnCloseListener)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getError()
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetHeight()
androidx.constraintlayout.solver.SolverVariable$Type: androidx.constraintlayout.solver.SolverVariable$Type valueOf(java.lang.String)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsetsAnimation$Callback getAnimationCallback()
io.flutter.embedding.engine.FlutterJNI: void setAsyncWaitForVsyncDelegate(io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate)
com.google.android.material.textfield.TextInputLayout: void setMinWidthResource(int)
com.google.android.material.textfield.TextInputLayout: void setEndIconDrawable(int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushTransform(float[])
androidx.core.graphics.drawable.IconCompat: IconCompat()
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: void setPresentationView(android.view.View)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
androidx.core.app.AppOpsManagerCompat$Api29Impl: java.lang.String getOpPackageName(android.content.Context)
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
androidx.browser.browseractions.BrowserActionsFallbackMenuView: BrowserActionsFallbackMenuView(android.content.Context,android.util.AttributeSet)
androidx.constraintlayout.widget.VirtualLayout: void setVisibility(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: FlutterRenderer$ImageReaderSurfaceProducer(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: android.view.accessibility.AccessibilityNodeInfo$ExtraRenderingInfo getExtraRenderingInfo(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin: SharedPreferencesPlugin()
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setOnClickListener(android.view.View$OnClickListener)
androidx.core.view.ViewCompat$Api21Impl: void setNestedScrollingEnabled(android.view.View,boolean)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void detachFromGLContext()
androidx.constraintlayout.widget.ConstraintHelper: void setReferencedIds(int[])
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getThumbDrawable()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setShortcut(android.view.MenuItem,char,char,int,int)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeColor(int)
com.google.android.material.internal.ForegroundLinearLayout: android.graphics.drawable.Drawable getForeground()
com.google.android.material.textfield.TextInputLayout: TextInputLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
androidx.appcompat.view.menu.MenuPopupHelper$Api17Impl: void getRealSize(android.view.Display,android.graphics.Point)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void maybeWaitOnFence(android.media.Image)
androidx.preference.Preference: Preference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
de.blinkt.openvpn.LaunchVPN: LaunchVPN()
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
androidx.core.content.res.FontResourcesParserCompat$Api21Impl: int getType(android.content.res.TypedArray,int)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType[] values()
com.google.android.material.button.MaterialButton: void setBackgroundResource(int)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.embedding.engine.FlutterJNI: void nativeInit(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
com.google.android.material.internal.ForegroundLinearLayout: void setForeground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
com.google.android.material.button.MaterialButton: void setIconResource(int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
androidx.core.view.ViewConfigurationCompat$Api28Impl: boolean shouldShowMenuShortcutsWhenKeyboardPresent(android.view.ViewConfiguration)
com.google.android.material.button.MaterialButton: int getIconSize()
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation[] values()
com.google.android.material.textfield.TextInputLayout: float getHintCollapsedTextHeight()
androidx.core.view.ViewConfigurationCompat$Api28Impl: int getScaledHoverSlop(android.view.ViewConfiguration)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin: LegacySharedPreferencesPlugin()
io.flutter.plugins.urllauncher.UrlLauncherPlugin: UrlLauncherPlugin()
com.google.android.material.textfield.TextInputLayout: void setStartIconDrawable(int)
androidx.fragment.app.DialogFragment: DialogFragment()
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
com.google.android.material.chip.Chip: void setChipIconEnabled(boolean)
androidx.constraintlayout.helper.widget.Flow: void setPaddingRight(int)
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
com.google.android.material.datepicker.MaterialDatePicker: MaterialDatePicker()
androidx.core.app.NotificationCompatBuilder$Api20Impl: java.lang.String getGroup(android.app.Notification)
io.flutter.embedding.engine.FlutterJNI: void attachToNative()
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setSettingsText(android.app.Notification$Builder,java.lang.CharSequence)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder addAction(android.app.Notification$Builder,android.app.Notification$Action)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
com.google.android.material.search.SearchView$Behavior: SearchView$Behavior(android.content.Context,android.util.AttributeSet)
io.flutter.view.AccessibilityViewEmbedder: void cacheVirtualIdMappings(android.view.View,int,int)
androidx.appcompat.widget.AppCompatEditText: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Api16Impl: android.text.StaticLayout createStaticLayoutForMeasuring(java.lang.CharSequence,android.text.Layout$Alignment,int,android.widget.TextView,android.text.TextPaint)
androidx.coordinatorlayout.widget.CoordinatorLayout: int getNestedScrollAxes()
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQueryHint()
androidx.core.widget.ImageViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getImageTintMode(android.widget.ImageView)
io.flutter.embedding.engine.FlutterOverlaySurface: int getId()
androidx.appcompat.widget.SwitchCompat: void setTextOn(java.lang.CharSequence)
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportButtonTintList(android.content.res.ColorStateList)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setOnClickListener(android.view.View$OnClickListener)
androidx.constraintlayout.solver.widgets.ConstraintWidget$DimensionBehaviour: androidx.constraintlayout.solver.widgets.ConstraintWidget$DimensionBehaviour[] values()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api26Impl: void setKeyboardNavigationCluster(android.view.View,boolean)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode[] values()
com.google.android.material.button.MaterialButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
androidx.appcompat.widget.AppCompatEditText: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
com.google.android.material.snackbar.SnackbarContentLayout: void setMaxInlineActionWidth(int)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void removeRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
com.google.android.material.chip.Chip: void setChecked(boolean)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceWindowChanged(android.view.Surface)
com.google.android.material.chip.Chip: void setTextAppearance(com.google.android.material.resources.TextAppearance)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
androidx.constraintlayout.widget.ConstraintLayout: void setOptimizationLevel(int)
kotlin.random.Random: Random()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.view.TextureRegistry$ImageTextureEntry: void pushImage(android.media.Image)
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.SwitchCompat: void setTrackResource(int)
androidx.core.widget.EdgeEffectCompat$Api21Impl: void onPull(android.widget.EdgeEffect,float,float)
androidx.recyclerview.widget.RecyclerView: void setChildDrawingOrderCallback(androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback)
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
com.google.android.material.snackbar.SnackbarContentLayout: SnackbarContentLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceChanged(long,int,int)
androidx.appcompat.widget.SearchView: void setImeOptions(int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture()
androidx.recyclerview.widget.RecyclerView: RecyclerView(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: android.text.Layout$Alignment getGravityTextAlignment()
de.blinkt.openvpn.core.ConnectionStatus: de.blinkt.openvpn.core.ConnectionStatus[] values()
androidx.appcompat.widget.SearchView: void setSubmitButtonEnabled(boolean)
com.google.android.material.textfield.TextInputLayout: void setBoxCollapsedPaddingTop(int)
androidx.appcompat.widget.AppCompatReceiveContentHelper$OnDropApi24Impl: boolean onDropForView(android.view.DragEvent,android.view.View,android.app.Activity)
io.flutter.view.FlutterCallbackInformation: io.flutter.view.FlutterCallbackInformation lookupCallbackInformation(long)
de.blinkt.openvpn.core.OpenVPNManagement$pauseReason: de.blinkt.openvpn.core.OpenVPNManagement$pauseReason valueOf(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo convertToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,int,android.view.View)
com.google.android.material.button.MaterialButton: void setIconPadding(int)
com.google.android.material.chip.Chip: void setCloseIconEndPadding(float)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
com.google.android.material.textfield.TextInputLayout: void setHintTextAppearance(int)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setGroupAlertBehavior(android.app.Notification$Builder,int)
androidx.appcompat.widget.Toolbar: int getPopupTheme()
com.google.android.material.chip.Chip: void setHideMotionSpecResource(int)
com.google.android.material.button.MaterialButton: android.graphics.PorterDuff$Mode getIconTintMode()
com.google.android.material.textfield.TextInputLayout: android.widget.TextView getPrefixTextView()
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.String[] getDigitStrings(android.icu.text.DecimalFormatSymbols)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder valueOf(java.lang.String)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void updateDisplayMetrics(int,float,float,float)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap decodeImage(java.nio.ByteBuffer,long)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void getTransformMatrix(float[])
com.google.android.material.textfield.TextInputLayout: void setCounterTextColor(android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getVMServiceUri()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getResId(java.lang.Object)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemRowTitle(java.lang.Object)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.AppCompatTextView: void setEmojiCompatEnabled(boolean)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.google.android.material.chip.Chip: void setCloseIconSize(float)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image acquireLatestImage()
androidx.preference.SwitchPreference: SwitchPreference(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: java.lang.String getA11yClassName()
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType[] values()
androidx.core.view.ViewCompat$Api26Impl: boolean isKeyboardNavigationCluster(android.view.View)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
com.google.android.material.chip.Chip: void setShapeAppearanceModel(com.google.android.material.shape.ShapeAppearanceModel)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$302(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer,boolean)
com.google.android.material.textfield.TextInputLayout: void setMaxWidthResource(int)
io.flutter.embedding.engine.FlutterJNI: long nativeAttach(io.flutter.embedding.engine.FlutterJNI)
kotlin.collections.AbstractMutableList: AbstractMutableList()
com.google.android.material.textfield.TextInputLayout: int getBoxStrokeWidthFocused()
androidx.core.widget.PopupWindowCompat$Api23Impl: int getWindowLayoutType(android.widget.PopupWindow)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.constraintlayout.helper.widget.Flow: void setFirstHorizontalBias(float)
com.google.android.material.button.MaterialButton: void setPressed(boolean)
androidx.constraintlayout.widget.Barrier: void setMargin(int)
com.google.android.material.button.MaterialButton: void setRippleColor(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setSuffixTextAppearance(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
com.google.android.material.button.MaterialButton: android.graphics.PorterDuff$Mode getBackgroundTintMode()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
androidx.appcompat.widget.AppCompatEditText: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
com.google.android.material.chip.Chip: void setBackgroundColor(int)
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
androidx.appcompat.widget.SearchView: androidx.cursoradapter.widget.CursorAdapter getSuggestionsAdapter()
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo,long)
androidx.core.view.ViewCompat$Api26Impl: boolean hasExplicitFocusable(android.view.View)
com.google.android.material.button.MaterialButtonToggleGroup: void setGeneratedIdIfNeeded(com.google.android.material.button.MaterialButton)
io.flutter.view.AccessibilityViewEmbedder: boolean performAction(int,int,android.os.Bundle)
io.flutter.view.AccessibilityViewEmbedder: void copyAccessibilityFields(android.view.accessibility.AccessibilityNodeInfo,android.view.accessibility.AccessibilityNodeInfo)
androidx.constraintlayout.widget.Barrier: void setDpMargin(int)
androidx.appcompat.widget.MenuPopupWindow$Api29Impl: void setTouchModal(android.widget.PopupWindow,boolean)
com.google.android.material.floatingactionbutton.FloatingActionButton$BaseBehavior: FloatingActionButton$BaseBehavior()
com.google.android.material.button.MaterialButton: void setInsetTop(int)
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
com.google.android.material.textfield.TextInputLayout: void setHint(int)
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
io.flutter.embedding.engine.FlutterJNI: void ensureNotAttachedToNative()
io.flutter.embedding.engine.FlutterJNI: void addIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
com.google.android.material.button.MaterialButton: void setA11yClassName(java.lang.String)
com.google.android.material.chip.Chip: void setEnsureMinTouchTargetSize(boolean)
androidx.appcompat.widget.AppCompatEditText: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
io.flutter.embedding.engine.FlutterJNI: void dispatchPlatformMessage(java.lang.String,java.nio.ByteBuffer,int,int)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
com.google.android.material.ripple.RippleUtils$RippleUtilsLollipop: android.graphics.drawable.Drawable createOvalRipple(android.content.Context,int)
com.google.android.material.textfield.TextInputLayout: int getBaseline()
androidx.core.view.ViewCompat$Api31Impl: void setOnReceiveContentListener(android.view.View,java.lang.String[],androidx.core.view.OnReceiveContentListener)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
androidx.preference.CheckBoxPreference: CheckBoxPreference(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: int getTextLayoutWidth()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
io.flutter.plugin.platform.SingleViewPresentation: void onCreate(android.os.Bundle)
com.google.android.material.internal.ClippableRoundedCornerLayout: ClippableRoundedCornerLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeMinTextSize()
androidx.appcompat.widget.SearchView: int getMaxWidth()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
com.google.android.material.chip.Chip: float getChipCornerRadius()
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setSound(android.app.Notification$Builder,android.net.Uri,java.lang.Object)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
com.google.android.gms.common.SupportErrorDialogFragment: SupportErrorDialogFragment()
kotlinx.coroutines.android.AndroidExceptionPreHandler: void handleException(kotlin.coroutines.CoroutineContext,java.lang.Throwable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getWidth()
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo,boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void waitOnFence(android.media.Image)
io.flutter.view.TextureRegistry$SurfaceProducer: void scheduleFrame()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder addPerson(android.app.Notification$Builder,java.lang.String)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: int getAnimationMode()
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.SwitchCompat: void setThumbResource(int)
androidx.appcompat.resources.Compatibility$Api15Impl: void getValueForDensity(android.content.res.Resources,int,int,android.util.TypedValue,boolean)
androidx.constraintlayout.widget.Barrier: int getMargin()
com.google.android.material.appbar.MaterialToolbar: void setElevation(float)
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode valueOf(java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: android.graphics.PorterDuff$Mode getIconTintMode(android.view.MenuItem)
com.google.android.material.datepicker.MaterialCalendar: MaterialCalendar()
androidx.core.view.ViewCompat$Api21Impl: void setElevation(android.view.View,float)
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.MainCoroutineDispatcher createDispatcher(java.util.List)
com.google.android.material.internal.TouchObserverFrameLayout: void setOnTouchListener(android.view.View$OnTouchListener)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$300(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.constraintlayout.widget.ConstraintLayout: int getMinWidth()
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
com.google.android.material.textfield.TextInputLayout: void setCounterTextAppearance(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.appcompat.widget.SwitchCompat: int getThumbScrollRange()
androidx.appcompat.resources.Compatibility$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetLeft(android.view.DisplayCutout)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchEmptyPlatformMessage(long,java.lang.String,int)
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness[] values()
com.google.android.material.textfield.TextInputLayout: int getBoxBackgroundColor()
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getHintTextColor()
androidx.constraintlayout.widget.ConstraintLayout: int getMaxHeight()
id.laskarmedia.openvpn_flutter.OpenVPNFlutterPlugin: OpenVPNFlutterPlugin()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: double deltaMillis(long)
io.flutter.view.AccessibilityViewEmbedder: boolean onAccessibilityHoverEvent(int,android.view.MotionEvent)
com.google.android.material.textfield.TextInputLayout: android.widget.TextView getSuffixTextView()
com.google.android.material.chip.Chip: java.lang.CharSequence getChipText()
com.google.android.material.textfield.TextInputLayout: int getBoxStrokeWidth()
com.google.android.material.textfield.TextInputLayout: void setEndIconMinSize(int)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPlatformMessage(long,java.lang.String,java.nio.ByteBuffer,int,int)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType valueOf(java.lang.String)
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getCloseIcon()
androidx.coordinatorlayout.widget.CoordinatorLayout: java.util.List getDependencySortedChildren()
androidx.recyclerview.widget.RecyclerView: void setLayoutFrozen(boolean)
androidx.core.app.NotificationCompatBuilder$Api31Impl: android.app.Notification$Builder setForegroundServiceBehavior(android.app.Notification$Builder,int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
androidx.core.widget.TextViewCompat$Api23Impl: void setBreakStrategy(android.widget.TextView,int)
io.flutter.embedding.engine.FlutterJNI: boolean nativeShouldDisableAHB()
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingRight()
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
io.flutter.view.AccessibilityViewEmbedder: java.lang.Integer getRecordFlutterId(android.view.View,android.view.accessibility.AccessibilityRecord)
com.google.android.material.textfield.TextInputLayout: int getEndIconMinSize()
androidx.fragment.app.strictmode.FragmentStrictMode$Flag: androidx.fragment.app.strictmode.FragmentStrictMode$Flag valueOf(java.lang.String)
com.google.android.material.snackbar.SnackbarContentLayout: android.widget.TextView getMessageView()
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.appcompat.widget.AppCompatCheckBox: int getCompoundPaddingLeft()
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.core.view.VelocityTrackerCompat$Api34Impl: boolean isAxisSupported(android.view.VelocityTracker,int)
com.google.android.material.textfield.TextInputLayout: void setEndIconDrawable(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
com.google.android.material.internal.BaselineLayout: int getBaseline()
com.google.android.material.textfield.TextInputLayout: void setErrorIconTintMode(android.graphics.PorterDuff$Mode)
androidx.core.widget.PopupWindowCompat$Api23Impl: void setWindowLayoutType(android.widget.PopupWindow,int)
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
androidx.core.graphics.Insets$Api29Impl: android.graphics.Insets of(int,int,int,int)
com.google.android.material.button.MaterialButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.google.android.material.chip.Chip: void setCloseIconEndPaddingResource(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: ImeSyncDeferringInsetsCallback$AnimationCallback(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
com.google.android.material.chip.Chip: void setChipStartPadding(float)
androidx.coordinatorlayout.widget.CoordinatorLayout: int getSuggestedMinimumWidth()
com.google.android.material.internal.NavigationMenuItemView: NavigationMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatCheckBox: void setFilters(android.text.InputFilter[])
com.google.android.material.chip.Chip: com.google.android.material.resources.TextAppearance getTextAppearance()
androidx.recyclerview.widget.RecyclerView: void setScrollState(int)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawableForDensity(android.content.res.Resources,int,int,android.content.res.Resources$Theme)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmoji(int)
de.blinkt.openvpn.core.DeviceStateReceiver$connectState: de.blinkt.openvpn.core.DeviceStateReceiver$connectState valueOf(java.lang.String)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase[] values()
androidx.appcompat.widget.SearchView: void setMaxWidth(int)
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
com.google.android.material.button.MaterialButton: void setBackground(android.graphics.drawable.Drawable)
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Action$Builder createBuilder(int,java.lang.CharSequence,android.app.PendingIntent)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: boolean isTextSelectable(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$300(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
com.google.android.material.textfield.TextInputLayout: void setEndIconScaleType(android.widget.ImageView$ScaleType)
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection[] values()
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleEnabled(boolean)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void addRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims()
androidx.appcompat.widget.SwitchCompat: void setTrackDrawable(android.graphics.drawable.Drawable)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getBackgroundTintList()
com.google.android.material.textfield.TextInputLayout: int getHintCurrentCollapsedTextColor()
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: boolean setLayoutDirection(android.graphics.drawable.Drawable,int)
io.flutter.plugins.pathprovider.PathProviderPlugin: PathProviderPlugin()
androidx.startup.InitializationProvider: InitializationProvider()
com.google.android.material.textfield.TextInputLayout: void setHintAnimationEnabled(boolean)
io.flutter.embedding.engine.FlutterJNI: void registerImageTexture(long,io.flutter.view.TextureRegistry$ImageConsumer)
androidx.appcompat.widget.SearchView: int getSuggestionCommitIconResId()
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix getFinalMatrix()
com.google.android.material.chip.Chip: void setCloseIconEnabled(boolean)
androidx.recyclerview.widget.RecyclerView: int getScrollState()
com.google.android.material.textfield.TextInputLayout: int getPlaceholderTextAppearance()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback access$200(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.plugin.platform.PlatformViewWrapper: void setTouchProcessor(io.flutter.embedding.android.AndroidTouchProcessor)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage dequeueImage()
com.google.android.material.internal.NavigationMenuItemView: void setIconPadding(int)
androidx.appcompat.widget.Toolbar: void setTitle(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
com.google.android.material.textfield.TextInputLayout: void setErrorIconDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
androidx.activity.OnBackPressedDispatcher$Api33Impl: void unregisterOnBackInvokedCallback(java.lang.Object,java.lang.Object)
androidx.appcompat.widget.SwitchCompat: int getThumbTextPadding()
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean hasRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
com.google.android.material.button.MaterialButton: int getTextHeight()
io.flutter.view.TextureRegistry$SurfaceProducer: void setSize(int,int)
com.google.android.material.transformation.ExpandableTransformationBehavior: ExpandableTransformationBehavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
com.google.android.material.internal.NavigationMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabled(boolean)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.MenuItemCompat$Api26Impl: android.content.res.ColorStateList getIconTintList(android.view.MenuItem)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifierBase(int)
androidx.recyclerview.widget.RecyclerView: void setClipToPadding(boolean)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleContentDescription(int)
androidx.core.view.ViewCompat$Api21Impl: float getElevation(android.view.View)
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void install()
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI spawn(java.lang.String,java.lang.String,java.lang.String,java.util.List)
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
androidx.constraintlayout.helper.widget.Flow: void setOrientation(int)
com.google.android.material.chip.Chip: void setChipIconEnabledResource(int)
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getPasswordVisibilityToggleDrawable()
androidx.activity.Api34Impl: float progress(android.window.BackEvent)
com.google.android.material.transformation.FabTransformationBehavior: FabTransformationBehavior()
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
androidx.transition.Transition$Impl26: void setCurrentPlayTime(android.animation.Animator,long)
com.google.android.material.timepicker.ClockFaceView: ClockFaceView(android.content.Context,android.util.AttributeSet)
com.google.android.material.appbar.MaterialToolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
com.google.android.material.textfield.TextInputLayout: void setEndIconVisible(boolean)
androidx.constraintlayout.helper.widget.Flow: void setPaddingTop(int)
androidx.core.view.ViewCompat$Api21Impl: android.content.res.ColorStateList getBackgroundTintList(android.view.View)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setInsets(int,androidx.core.graphics.Insets)
androidx.core.app.NotificationCompatBuilder$Api23Impl: android.app.Notification$Builder setSmallIcon(android.app.Notification$Builder,java.lang.Object)
androidx.constraintlayout.widget.ConstraintAttribute$AttributeType: androidx.constraintlayout.widget.ConstraintAttribute$AttributeType valueOf(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
com.google.android.material.chip.Chip: com.google.android.material.shape.ShapeAppearanceModel getShapeAppearanceModel()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreScroll(android.view.View,int,int,int[],int[])
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintList(android.view.View,android.content.res.ColorStateList)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getThumbTintList()
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int,int)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingLeft()
com.google.android.material.button.MaterialButton: void setCornerRadiusResource(int)
io.flutter.embedding.engine.FlutterJNI: void setLocalizationPlugin(io.flutter.plugin.localization.LocalizationPlugin)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
androidx.appcompat.widget.SwitchCompat: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
kotlinx.coroutines.android.AndroidDispatcherFactory: int getLoadPriority()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo createAccessibilityNodeInfo(int)
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getEditTextBoxBackground()
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
com.google.android.material.chip.Chip: void setChipStrokeColorResource(int)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: void stopNestedScroll(android.view.View)
androidx.core.view.DisplayCutoutCompat$Api28Impl: android.view.DisplayCutout createDisplayCutout(android.graphics.Rect,java.util.List)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState valueOf(java.lang.String)
androidx.activity.Api34Impl: float touchX(android.window.BackEvent)
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
io.flutter.plugins.urllauncher.WebViewActivity: WebViewActivity()
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
com.google.android.material.textfield.TextInputLayout: void setErrorAccessibilityLiveRegion(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Action$Builder setAllowGeneratedReplies(android.app.Notification$Action$Builder,boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeScheduleFrame(long)
androidx.constraintlayout.widget.ConstraintHelper: void setIds(java.lang.String)
androidx.appcompat.widget.SearchView: void setQuery(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
com.google.android.material.textfield.TextInputLayout: void setStartIconContentDescription(java.lang.CharSequence)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag[] values()
androidx.appcompat.view.menu.ActionMenuItemView: java.lang.CharSequence getAccessibilityClassName()
com.google.android.material.button.MaterialButton: int getInsetTop()
androidx.coordinatorlayout.widget.CoordinatorLayout: android.graphics.drawable.Drawable getStatusBarBackground()
com.google.android.gms.common.api.GoogleApiActivity: GoogleApiActivity()
com.google.android.material.chip.Chip: void setCloseIconPressed(boolean)
de.blinkt.openvpn.core.ConfigParser$linestate: de.blinkt.openvpn.core.ConfigParser$linestate[] values()
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State[] values()
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
com.google.android.material.textfield.TextInputEditText: void setTextInputLayoutFocusedRectEnabled(boolean)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.core.widget.CompoundButtonCompat$Api21Impl: void setButtonTintMode(android.widget.CompoundButton,android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState[] values()
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleTintList(android.content.res.ColorStateList)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action valueOf(java.lang.String)
com.google.android.material.chip.Chip: void setChipDrawable(com.google.android.material.chip.ChipDrawable)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRRect(int,int,int,int,float[])
com.google.android.material.button.MaterialButton: void setRippleColorResource(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getParent(android.view.accessibility.AccessibilityNodeInfo,int)
com.google.android.material.button.MaterialButton: void setShapeAppearanceModel(com.google.android.material.shape.ShapeAppearanceModel)
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.AppCompatButton: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
com.google.android.material.internal.NavigationMenuItemView: void setTextAppearance(int)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: float getBackgroundOverlayColorAlpha()
androidx.constraintlayout.widget.Guideline: void setGuidelinePercent(float)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: FlutterRenderer$ImageTextureRegistryEntry(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.recyclerview.widget.RecyclerView: void setAccessibilityDelegateCompat(androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate)
androidx.appcompat.widget.SwitchCompat: void setTextOffInternal(java.lang.CharSequence)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void finalize()
com.google.android.material.chip.Chip: void setBackgroundResource(int)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
com.google.android.material.button.MaterialButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setKeyListener(android.text.method.KeyListener)
androidx.activity.Api34Impl: float touchY(android.window.BackEvent)
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture,java.lang.Runnable)
androidx.constraintlayout.widget.Guideline: void setVisibility(int)
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintList(android.widget.ImageView,android.content.res.ColorStateList)
androidx.activity.Api34Impl: int swipeEdge(android.window.BackEvent)
io.flutter.plugins.GeneratedPluginRegistrant: void registerWith(io.flutter.embedding.engine.FlutterEngine)
com.google.android.material.transformation.FabTransformationSheetBehavior: FabTransformationSheetBehavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
androidx.appcompat.widget.AppCompatTextHelper$Api21Impl: java.util.Locale forLanguageTag(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: void addChildrenToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
com.google.android.material.chip.Chip: void setTextEndPaddingResource(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id()
com.google.android.material.chip.Chip: void setTextEndPadding(float)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.Object createRangeInfo(int,float,float,float)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemColumnTitle(java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
com.google.android.material.internal.ForegroundLinearLayout: int getForegroundGravity()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$302(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedFling(android.view.ViewParent,android.view.View,float,float,boolean)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode valueOf(java.lang.String)
com.google.android.material.chip.Chip: void setCheckedIconTint(android.content.res.ColorStateList)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setAlphabeticShortcut(android.view.MenuItem,char,int)
androidx.preference.UnPressableLinearLayout: UnPressableLinearLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
com.google.android.material.textfield.TextInputLayout: void setCounterMaxLength(int)
com.google.android.material.textfield.TextInputLayout: void setEndIconTintMode(android.graphics.PorterDuff$Mode)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Icon toIcon(androidx.core.graphics.drawable.IconCompat,android.content.Context)
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int,int)
androidx.constraintlayout.widget.ConstraintLayout: void setOnConstraintsChanged(androidx.constraintlayout.widget.ConstraintsChangedListener)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.core.view.MenuItemCompat$Api26Impl: int getAlphabeticModifiers(android.view.MenuItem)
androidx.constraintlayout.widget.ConstraintLayout: int getMinHeight()
com.google.android.material.button.MaterialButtonToggleGroup: int getFirstVisibleChildIndex()
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface()
com.google.android.material.textfield.TextInputLayout: void setErrorContentDescription(java.lang.CharSequence)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void release()
com.google.android.material.chip.Chip: void setCheckedIconEnabledResource(int)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
com.google.android.material.floatingactionbutton.FloatingActionButton$BaseBehavior: FloatingActionButton$BaseBehavior(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness valueOf(java.lang.String)
com.google.android.material.textfield.TextInputLayout: void setPrefixText(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageResponseCallback(long,int,java.nio.ByteBuffer,int)
androidx.fragment.app.DefaultSpecialEffectsController$Api24Impl: long totalDuration(android.animation.AnimatorSet)
androidx.constraintlayout.helper.widget.Flow: void setHorizontalAlign(int)
com.google.android.material.chip.Chip: android.graphics.Rect getCloseIconTouchBoundsInt()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.recyclerview.widget.RecyclerView: void setAdapter(androidx.recyclerview.widget.RecyclerView$Adapter)
io.flutter.view.TextureRegistry$ImageConsumer: android.media.Image acquireLatestImage()
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOn()
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
androidx.browser.customtabs.CustomTabsIntent$Api24Impl: java.lang.String getDefaultLocale()
com.google.android.material.chip.Chip: android.content.res.ColorStateList getChipIconTint()
com.google.android.material.chip.Chip: void setRippleColor(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
androidx.core.view.ViewCompat$Api28Impl: java.lang.Object requireViewById(android.view.View,int)
androidx.appcompat.widget.SwitchCompat: void setChecked(boolean)
androidx.core.graphics.drawable.IconCompat$Api28Impl: java.lang.String getResPackage(java.lang.Object)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: java.lang.CharSequence getContainerTitle(android.view.accessibility.AccessibilityNodeInfo)
androidx.recyclerview.widget.RecyclerView: void setOnFlingListener(androidx.recyclerview.widget.RecyclerView$OnFlingListener)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
com.google.android.material.datepicker.MaterialTextInputPicker: MaterialTextInputPicker()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setTooltipText(android.view.MenuItem,java.lang.CharSequence)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType[] values()
com.google.android.material.chip.Chip: void setOnCloseIconClickListener(android.view.View$OnClickListener)
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
com.google.android.material.button.MaterialButton: void setStrokeColorResource(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.core.widget.TextViewCompat$Api28Impl: android.text.PrecomputedText$Params getTextMetricsParams(android.widget.TextView)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap nativeGetBitmap(long)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,int,java.nio.ByteBuffer,int)
com.google.android.material.internal.CheckableImageButton: void setChecked(boolean)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedPreFling(android.view.ViewParent,android.view.View,float,float)
com.google.android.material.chip.Chip: void setGravity(int)
com.google.android.material.drawable.DrawableUtils$OutlineCompatL: void setConvexPath(android.graphics.Outline,android.graphics.Path)
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type[] values()
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
de.blinkt.openvpn.core.VpnStatus$LogLevel: de.blinkt.openvpn.core.VpnStatus$LogLevel[] values()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: AppCompatTextViewAutoSizeHelper$Impl23()
androidx.appcompat.widget.SearchView: int getInputType()
androidx.core.view.DisplayCutoutCompat$Api28Impl: java.util.List getBoundingRects(android.view.DisplayCutout)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void startRearDisplayPresentationSession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
androidx.recyclerview.widget.RecyclerView: void setLayoutManager(androidx.recyclerview.widget.RecyclerView$LayoutManager)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
com.google.android.material.chip.Chip: void setCheckedIconVisible(boolean)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.core.widget.TextViewCompat$Api23Impl: int getBreakStrategy(android.widget.TextView)
io.flutter.embedding.engine.FlutterJNI: void markTextureFrameAvailable(long)
com.google.android.material.timepicker.TimePickerView: TimePickerView(android.content.Context,android.util.AttributeSet)
androidx.constraintlayout.helper.widget.Flow: void setPaddingBottom(int)
com.google.android.material.textfield.TextInputLayout: void setDefaultHintTextColor(android.content.res.ColorStateList)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void endRearDisplaySession()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onPrepare(android.view.WindowInsetsAnimation)
io.flutter.embedding.engine.FlutterJNI: long performNativeAttach(io.flutter.embedding.engine.FlutterJNI)
kotlin.time.DurationUnit: kotlin.time.DurationUnit[] values()
androidx.appcompat.widget.AppCompatButton: void setSupportAllCaps(boolean)
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.recyclerview.widget.RecyclerView: boolean getClipToPadding()
androidx.appcompat.widget.AppCompatCheckBox: androidx.appcompat.widget.AppCompatEmojiTextHelper getEmojiTextViewHelper()
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo getRootNode(android.view.View,int,android.graphics.Rect)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$Adapter getAdapter()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onEnd(android.view.WindowInsetsAnimation)
com.google.android.material.textfield.TextInputLayout: void setBoxBackgroundColor(int)
androidx.appcompat.widget.AppCompatTextHelper$Api17Impl: android.graphics.drawable.Drawable[] getCompoundDrawablesRelative(android.widget.TextView)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void attachToGLContext(int)
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: int getBaseline()
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate getCompatAccessibilityDelegate()
androidx.preference.MultiSelectListPreference: MultiSelectListPreference(android.content.Context,android.util.AttributeSet)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setStatusBarBackgroundColor(int)
androidx.constraintlayout.helper.widget.Flow: void setPaddingLeft(int)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
androidx.recyclerview.widget.RecyclerView: androidx.core.view.NestedScrollingChildHelper getScrollingChildHelper()
androidx.core.view.animation.PathInterpolatorCompat$Api21Impl: android.view.animation.Interpolator createPathInterpolator(android.graphics.Path)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
androidx.window.extensions.core.util.function.Predicate: boolean test(java.lang.Object)
androidx.preference.internal.PreferenceImageView: void setMaxHeight(int)
androidx.appcompat.widget.SwitchCompat: void setThumbPosition(float)
androidx.preference.PreferenceScreen: PreferenceScreen(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: float getChipIconSize()
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.core.app.NotificationCompatBuilder$Api28Impl: android.app.Notification$Action$Builder setSemanticAction(android.app.Notification$Action$Builder,int)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax valueOf(java.lang.String)
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void remove()
androidx.core.view.ViewCompat$Api28Impl: void setScreenReaderFocusable(android.view.View,boolean)
androidx.recyclerview.widget.RecyclerView: void setOnScrollListener(androidx.recyclerview.widget.RecyclerView$OnScrollListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
com.google.android.material.textfield.TextInputLayout: void setStartIconMinSize(int)
androidx.appcompat.widget.AppCompatEditText: void setKeyListener(android.text.method.KeyListener)
androidx.core.widget.NestedScrollView$Api21Impl: boolean getClipToPadding(android.view.ViewGroup)
androidx.appcompat.widget.AppCompatTextHelper$Api24Impl: void setTextLocales(android.widget.TextView,android.os.LocaleList)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImages()
com.google.android.material.chip.Chip: void setChipIcon(android.graphics.drawable.Drawable)
androidx.transition.ViewGroupUtils$Api29Impl: void suppressLayout(android.view.ViewGroup,boolean)
com.google.android.material.textfield.TextInputLayout: int getMaxWidth()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRect(int,int,int,int)
com.google.android.material.button.MaterialButtonToggleGroup: void setSelectionRequired(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.appcompat.widget.SwitchCompat: int getThumbOffset()
androidx.constraintlayout.widget.ConstraintLayout: void setMaxHeight(int)
androidx.appcompat.widget.AppCompatTextHelper$Api28Impl: android.graphics.Typeface create(android.graphics.Typeface,int,boolean)
com.google.android.material.button.MaterialButton: MaterialButton(android.content.Context,android.util.AttributeSet)
androidx.core.content.ContextCompat$Api23Impl: int getColor(android.content.Context,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
com.google.android.material.chip.Chip: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory getEdgeEffectFactory()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: void setStateDescription(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature valueOf(java.lang.String)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type valueOf(java.lang.String)
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
com.google.android.material.chip.Chip: void setChipEndPaddingResource(int)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getStrokeColor()
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatCheckBox: void setEmojiCompatEnabled(boolean)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeSetSemanticsEnabled(long,boolean)
androidx.constraintlayout.helper.widget.Flow: void setHorizontalGap(int)
androidx.constraintlayout.solver.widgets.ConstraintAnchor$Type: androidx.constraintlayout.solver.widgets.ConstraintAnchor$Type valueOf(java.lang.String)
androidx.constraintlayout.helper.widget.Flow: void setHorizontalStyle(int)
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
de.blinkt.openvpn.core.Connection$ProxyType: de.blinkt.openvpn.core.Connection$ProxyType[] values()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScrollAccepted(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$ItemAnimator getItemAnimator()
androidx.appcompat.widget.AppCompatTextHelper$Api26Impl: boolean setFontVariationSettings(android.widget.TextView,java.lang.String)
io.flutter.view.TextureRegistry$SurfaceProducer: void release()
com.google.android.material.textfield.TextInputLayout: float getBoxCornerRadiusTopStart()
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
androidx.recyclerview.widget.RecyclerView: void setNestedScrollingEnabled(boolean)
androidx.core.app.AppOpsManagerCompat$Api29Impl: int checkOpNoThrow(android.app.AppOpsManager,java.lang.String,int,java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void getBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
de.blinkt.openvpn.core.DeviceStateReceiver$connectState: de.blinkt.openvpn.core.DeviceStateReceiver$connectState[] values()
com.google.android.material.textfield.TextInputLayout: int getMinWidth()
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setShortcutId(android.app.Notification$Builder,java.lang.String)
androidx.appcompat.widget.SwitchCompat: androidx.appcompat.widget.AppCompatEmojiTextHelper getEmojiTextViewHelper()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchSemanticsAction(long,int,int,java.nio.ByteBuffer,int)
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType[] values()
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
androidx.core.content.ContextCompat$Api26Impl: android.content.ComponentName startForegroundService(android.content.Context,android.content.Intent)
androidx.core.widget.CompoundButtonCompat$Api21Impl: android.graphics.PorterDuff$Mode getButtonTintMode(android.widget.CompoundButton)
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeLoadDartDeferredLibrary(long,int,java.lang.String[])
io.flutter.embedding.engine.FlutterJNI: void lambda$decodeImage$0(long,android.graphics.ImageDecoder,android.graphics.ImageDecoder$ImageInfo,android.graphics.ImageDecoder$Source)
com.google.android.material.chip.Chip: void setChipEndPadding(float)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
androidx.appcompat.widget.SwitchCompat: void setEnforceSwitchWidth(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
androidx.constraintlayout.helper.widget.Flow: Flow(android.content.Context,android.util.AttributeSet)
com.google.android.material.behavior.HideBottomViewOnScrollBehavior: HideBottomViewOnScrollBehavior()
androidx.core.view.ViewCompat$Api29Impl: android.view.contentcapture.ContentCaptureSession getContentCaptureSession(android.view.View)
androidx.appcompat.widget.AppCompatButton: void setEmojiCompatEnabled(boolean)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int access$200(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
com.google.android.material.textfield.TextInputLayout: android.widget.EditText getEditText()
com.google.android.material.textfield.TextInputLayout: void setMaxWidth(int)
com.google.android.material.chip.Chip: void setCheckedIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
io.flutter.embedding.engine.FlutterJNI: void updateSemantics(java.nio.ByteBuffer,java.lang.String[],java.nio.ByteBuffer[])
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int)
com.google.android.material.carousel.CarouselLayoutManager: CarouselLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getOrCreateFilledDropDownMenuBackground()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder setGroupSummary(android.app.Notification$Builder,boolean)
io.flutter.embedding.engine.FlutterJNI: void nativePrefetchDefaultFontManager()
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
com.google.android.material.chip.Chip: void setElevation(float)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
androidx.lifecycle.SavedStateHandlesVM: SavedStateHandlesVM()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBackground(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessageResponse(int,java.nio.ByteBuffer)
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode[] values()
androidx.appcompat.widget.AppCompatButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getTrackDrawable()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
io.flutter.embedding.engine.FlutterJNI: void onBeginFrame()
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior: ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
com.google.android.material.textfield.TextInputLayout: void setEndIconOnClickListener(android.view.View$OnClickListener)
com.google.android.gms.common.api.internal.zzb: zzb()
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.recyclerview.widget.RecyclerView: boolean isLayoutSuppressed()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void nativeSetAccessibilityFeatures(long,int)
io.flutter.embedding.android.FlutterView: void setWindowInfoListenerDisplayFeatures(androidx.window.layout.WindowLayoutInfo)
com.google.android.material.textfield.TextInputEditText: java.lang.CharSequence getHint()
com.google.android.material.textfield.TextInputLayout: com.google.android.material.internal.CheckableImageButton getEndIconView()
com.google.android.material.textfield.TextInputLayout: void setSuffixTextColor(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void endRearDisplayPresentationSession()
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: int getWindowAreaStatus()
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodeParent(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifier(int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintList(android.graphics.drawable.Drawable,android.content.res.ColorStateList)
com.google.android.material.button.MaterialButton: void setInternalBackground(android.graphics.drawable.Drawable)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
com.google.android.material.transformation.FabTransformationBehavior: FabTransformationBehavior(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void unregisterTexture(long)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.constraintlayout.solver.widgets.analyzer.WidgetRun$RunType: androidx.constraintlayout.solver.widgets.analyzer.WidgetRun$RunType[] values()
androidx.transition.ObjectAnimatorUtils$Api21Impl: android.animation.ObjectAnimator ofObject(java.lang.Object,android.util.Property,android.graphics.Path)
androidx.core.app.RemoteInput$Api20Impl: android.os.Bundle getResultsFromIntent(android.content.Intent)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader33()
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
androidx.transition.ViewUtilsApi23$Api29Impl: void setTransitionVisibility(android.view.View,int)
androidx.core.view.ViewCompat$Api26Impl: int getImportantForAutofill(android.view.View)
androidx.core.app.NotificationCompatBuilder$Api29Impl: android.app.Notification$Action$Builder setContextual(android.app.Notification$Action$Builder,boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatButton: int[] getAutoSizeTextAvailableSizes()
com.google.android.material.textfield.TextInputLayout: int getStartIconMinSize()
androidx.core.graphics.PaintCompat$Api23Impl: boolean hasGlyph(android.graphics.Paint,java.lang.String)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
com.google.android.material.textfield.TextInputLayout: void setStartIconTintList(android.content.res.ColorStateList)
androidx.constraintlayout.solver.widgets.ConstraintWidget$DimensionBehaviour: androidx.constraintlayout.solver.widgets.ConstraintWidget$DimensionBehaviour valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
com.google.android.material.chip.Chip: void setTextAppearanceResource(int)
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.google.android.material.chip.Chip: com.google.android.material.animation.MotionSpec getHideMotionSpec()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
androidx.transition.Transition$Impl26: long getTotalDuration(android.animation.Animator)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedFling(android.view.View,float,float,boolean)
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getChipDrawable()
com.google.android.material.chip.Chip: void setCheckedIconVisible(int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
androidx.appcompat.widget.AppCompatButton: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOp(android.app.AppOpsManager,java.lang.String,java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
io.flutter.embedding.engine.FlutterJNI: void onRenderingStopped()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceDestroyed()
androidx.preference.DialogPreference: DialogPreference(android.content.Context,android.util.AttributeSet)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getBoxStrokeErrorColor()
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.CharSequence castToCharSequence(android.text.PrecomputedText)
com.google.android.material.textfield.TextInputLayout: void setCounterEnabled(boolean)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
com.google.android.material.textfield.TextInputLayout: void setError(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void onEndFrame()
androidx.appcompat.widget.AppCompatTextClassifierHelper$Api26Impl: android.view.textclassifier.TextClassifier getTextClassifier(android.widget.TextView)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.core.view.animation.PathInterpolatorCompat$Api21Impl: android.view.animation.Interpolator createPathInterpolator(float,float)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getUniqueId(android.view.accessibility.AccessibilityNodeInfo)
androidx.coordinatorlayout.widget.CoordinatorLayout: int getSuggestedMinimumHeight()
com.google.android.material.chip.Chip: void setIconStartPadding(float)
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getTrackTintMode()
androidx.appcompat.widget.SearchView: void setOnQueryTextFocusChangeListener(android.view.View$OnFocusChangeListener)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void updateTexImage()
com.google.android.material.internal.NavigationMenuItemView: void setTitle(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.google.android.material.chip.Chip: android.text.TextUtils$TruncateAt getEllipsize()
io.flutter.embedding.engine.FlutterJNI: void ensureRunningOnMainThread()
androidx.appcompat.widget.AppCompatTextView: androidx.appcompat.widget.AppCompatEmojiTextHelper getEmojiTextViewHelper()
androidx.appcompat.widget.Toolbar$Api33Impl: android.window.OnBackInvokedCallback newOnBackInvokedCallback(java.lang.Runnable)
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
com.google.android.material.internal.NavigationMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.profileinstaller.ProfileVerifier$Api33Impl: android.content.pm.PackageInfo getPackageInfo(android.content.pm.PackageManager,android.content.Context)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType[] values()
com.google.android.material.timepicker.ClockHandView: ClockHandView(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowCompat$Api30Impl: void setDecorFitsSystemWindows(android.view.Window,boolean)
androidx.recyclerview.widget.RecyclerView: int getItemDecorationCount()
com.google.android.material.textfield.TextInputLayout: void setEditText(android.widget.EditText)
com.google.android.material.textfield.TextInputLayout: void setStartIconOnClickListener(android.view.View$OnClickListener)
com.jrai.flutter_keyboard_visibility.FlutterKeyboardVisibilityPlugin: FlutterKeyboardVisibilityPlugin()
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder setGroup(android.app.Notification$Builder,java.lang.String)
androidx.fragment.app.FragmentContainerView: void setLayoutTransition(android.animation.LayoutTransition)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
com.google.android.material.chip.Chip: void setChipIconVisible(int)
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
androidx.fragment.app.strictmode.FragmentStrictMode$Flag: androidx.fragment.app.strictmode.FragmentStrictMode$Flag[] values()
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.android.material.textfield.TextInputLayout: void setEnabled(boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeOnVsync(long,long,long)
com.google.android.material.snackbar.BaseTransientBottomBar$Behavior: BaseTransientBottomBar$Behavior()
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getTrackTintList()
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledHorizontalScrollFactor(android.view.ViewConfiguration)
androidx.core.view.MenuItemCompat$Api26Impl: int getNumericModifiers(android.view.MenuItem)
androidx.appcompat.widget.AppCompatCheckBox: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnFrameConsumedListener(io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
com.google.android.material.button.MaterialButton: void setStrokeWidthResource(int)
androidx.appcompat.widget.DrawableUtils$Api29Impl: android.graphics.Insets getOpticalInsets(android.graphics.drawable.Drawable)
androidx.constraintlayout.widget.VirtualLayout: void setElevation(float)
io.flutter.embedding.engine.FlutterJNI: void nativeRunBundleAndSnapshotFromLibrary(long,java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
androidx.core.app.NotificationCompatBuilder$Api28Impl: android.app.Notification$Builder addPerson(android.app.Notification$Builder,android.app.Person)
androidx.core.view.ViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getBackgroundTintMode(android.view.View)
com.google.android.material.chip.Chip: java.lang.CharSequence getAccessibilityClassName()
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior: ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$100(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI nativeSpawn(long,java.lang.String,java.lang.String,java.lang.String,java.util.List)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
com.google.android.material.textfield.TextInputLayout: void setStartIconCheckable(boolean)
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
io.flutter.embedding.engine.FlutterJNI: void loadLibrary(android.content.Context)
com.google.android.material.chip.Chip: void setChipIconTintResource(int)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodesTranslateBounds(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect,android.view.accessibility.AccessibilityNodeInfo)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
com.google.android.material.chip.Chip: void setChipStrokeColor(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.constraintlayout.helper.widget.Flow: void setVerticalBias(float)
kotlinx.coroutines.flow.SharingCommand: kotlinx.coroutines.flow.SharingCommand[] values()
com.google.android.material.internal.BaselineLayout: BaselineLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType valueOf(java.lang.String)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void release()
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageResponseCallback(int,java.nio.ByteBuffer,int)
com.google.android.material.chip.Chip: void setHideMotionSpec(com.google.android.material.animation.MotionSpec)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceChanged(int,int)
androidx.core.view.ViewCompat$Api21Impl: boolean isNestedScrollingEnabled(android.view.View)
com.google.android.material.transformation.ExpandableTransformationBehavior: ExpandableTransformationBehavior()
com.google.android.gms.common.GooglePlayServicesMissingManifestValueException: GooglePlayServicesMissingManifestValueException()
androidx.core.app.RemoteInput$Api29Impl: android.app.RemoteInput$Builder setEditChoicesBeforeSending(android.app.RemoteInput$Builder,int)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void addRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setOnHierarchyChangeListener(android.view.ViewGroup$OnHierarchyChangeListener)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: boolean isHorizontallyScrollable(android.widget.TextView)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: long getMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setQueryFromAppProcessEnabled(android.view.accessibility.AccessibilityNodeInfo,android.view.View,boolean)
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController(io.flutter.plugin.platform.PlatformViewsController)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag valueOf(java.lang.String)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
com.google.android.material.timepicker.ChipTextInputComboView: ChipTextInputComboView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void onFirstFrame()
com.google.android.material.chip.Chip: void setLines(int)
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getEndIconContentDescription()
androidx.core.app.RemoteInput$Api26Impl: java.util.Set getAllowedDataTypes(java.lang.Object)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
com.google.android.material.button.MaterialButton: void setIconTintResource(int)
androidx.core.app.NotificationCompatBuilder$Api31Impl: android.app.Notification$Action$Builder setAuthenticationRequired(android.app.Notification$Action$Builder,boolean)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setVisibility(int)
io.flutter.embedding.engine.FlutterJNI: void dispatchPointerDataPacket(java.nio.ByteBuffer,int)
com.google.android.material.chip.Chip: void setCloseIconEnabledResource(int)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection[] values()
com.google.android.material.textfield.TextInputLayout: com.google.android.material.shape.ShapeAppearanceModel getShapeAppearanceModel()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
androidx.constraintlayout.solver.widgets.ConstraintAnchor$Type: androidx.constraintlayout.solver.widgets.ConstraintAnchor$Type[] values()
androidx.appcompat.widget.AppCompatButton: androidx.appcompat.widget.AppCompatEmojiTextHelper getEmojiTextViewHelper()
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode valueOf(java.lang.String)
com.google.android.material.textfield.TextInputLayout: void setPlaceholderTextEnabled(boolean)
com.google.android.material.chip.Chip: void setIconEndPaddingResource(int)
androidx.appcompat.widget.AppCompatEditText: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.SwitchCompat: void setTrackTintList(android.content.res.ColorStateList)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void pushImage(android.media.Image)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
com.google.android.material.chip.Chip: void setSingleLine(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void cleanup()
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setSize(int,int)
androidx.core.widget.PopupWindowCompat$Api23Impl: boolean getOverlapAnchor(android.widget.PopupWindow)
androidx.appcompat.widget.AppCompatButton: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
com.google.android.material.appbar.MaterialToolbar: void setTitleCentered(boolean)
io.flutter.view.AccessibilityViewEmbedder: boolean requestSendAccessibilityEvent(android.view.View,android.view.View,android.view.accessibility.AccessibilityEvent)
androidx.activity.OnBackPressedDispatcher$Api33Impl: void registerOnBackInvokedCallback(java.lang.Object,int,java.lang.Object)
com.google.android.material.chip.Chip: float getChipStrokeWidth()
androidx.appcompat.widget.SearchView: void setAppSearchData(android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPointerDataPacket(long,java.nio.ByteBuffer,int)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
com.google.android.material.chip.Chip: void setMaxLines(int)
io.flutter.embedding.engine.FlutterJNI: void nativeImageHeaderCallback(long,int,int)
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.recyclerview.widget.RecyclerView: void setLayoutTransition(android.animation.LayoutTransition)
androidx.constraintlayout.helper.widget.Flow: void setVerticalAlign(int)
com.google.android.material.chip.Chip: void setChipStrokeWidthResource(int)
com.google.android.material.button.MaterialButton: void setElevation(float)
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
com.google.android.material.button.MaterialButtonToggleGroup: void setupButtonChild(com.google.android.material.button.MaterialButton)
androidx.constraintlayout.solver.widgets.analyzer.WidgetRun$RunType: androidx.constraintlayout.solver.widgets.analyzer.WidgetRun$RunType valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: boolean getSplitTrack()
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointVariantSelector(int)
com.google.android.material.textfield.TextInputLayout: int getMinEms()
com.miguelruivo.flutter.plugin.countrycodes.country_codes.CountryCodesPlugin: CountryCodesPlugin()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void release()
androidx.constraintlayout.solver.SolverVariable$Type: androidx.constraintlayout.solver.SolverVariable$Type[] values()
com.google.android.material.button.MaterialButton: void setShouldDrawSurfaceColorStroke(boolean)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void applyTheme(android.graphics.drawable.Drawable,android.content.res.Resources$Theme)
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection valueOf(java.lang.String)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: android.graphics.ColorFilter getColorFilter(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
com.google.android.material.textfield.TextInputLayout: void setEndIconOnLongClickListener(android.view.View$OnLongClickListener)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.PlatformView getView()
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
com.google.android.material.textfield.TextInputLayout: int getMaxEms()
com.google.android.material.button.MaterialButton: void setIconTint(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatEditText: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintMode(android.graphics.drawable.Drawable,android.graphics.PorterDuff$Mode)
com.google.android.material.button.MaterialButton: void setIconSize(int)
com.google.android.material.textfield.TextInputLayout: void setEndIconContentDescription(java.lang.CharSequence)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQuery()
com.google.android.material.snackbar.Snackbar$SnackbarLayout: Snackbar$SnackbarLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.android.FlutterView: void setVisibility(int)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getErrorContentDescription()
androidx.core.view.ViewGroupCompat$Api21Impl: int getNestedScrollAxes(android.view.ViewGroup)
androidx.activity.Api34Impl: android.window.BackEvent createOnBackEvent(float,float,float,int)
androidx.appcompat.widget.AppCompatButton: void setAutoSizeTextTypeWithDefaults(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void onVsync(long,long,long)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImageReaders()
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
com.google.android.material.chip.Chip: float getTextEndPadding()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
com.google.android.material.chip.Chip: void setCloseIconStartPaddingResource(int)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.PlatformView,io.flutter.plugin.platform.AccessibilityEventsDelegate,int,android.view.View$OnFocusChangeListener)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind[] values()
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeWidth(int)
androidx.window.extensions.core.util.function.Function: java.lang.Object apply(java.lang.Object)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.recyclerview.widget.RecyclerView: void setHasFixedSize(boolean)
androidx.core.app.RemoteInput$Api26Impl: void addDataResultToIntent(androidx.core.app.RemoteInput,android.content.Intent,java.util.Map)
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
com.google.android.material.chip.Chip: void setBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
io.flutter.embedding.android.FlutterView: void setDelegate(io.flutter.embedding.android.FlutterViewDelegate)
io.flutter.plugin.platform.PlatformViewWrapper: android.view.ViewTreeObserver$OnGlobalFocusChangeListener getActiveFocusListener()
androidx.appcompat.widget.AppCompatButton: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.recyclerview.widget.RecyclerView: void setItemViewCacheSize(int)
androidx.core.os.ConfigurationCompat$Api24Impl: android.os.LocaleList getLocales(android.content.res.Configuration)
com.google.android.material.chip.Chip: android.graphics.RectF getCloseIconTouchBounds()
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
com.google.android.material.appbar.MaterialToolbar: android.widget.ImageView$ScaleType getLogoScaleType()
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getSuffixTextColor()
io.flutter.embedding.engine.FlutterOverlaySurface: FlutterOverlaySurface(int,android.view.Surface)
com.google.android.material.chip.Chip: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatTextView: void setFilters(android.text.InputFilter[])
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat$Api18Impl: void setAutoCancel(android.animation.ObjectAnimator,boolean)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
io.flutter.embedding.engine.FlutterJNI: io.flutter.view.FlutterCallbackInformation nativeLookupCallbackInformation(long)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat[] values()
androidx.core.view.ViewCompat$Api26Impl: int getNextClusterForwardId(android.view.View)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType valueOf(java.lang.String)
androidx.core.os.LocaleListCompat$Api24Impl: android.os.LocaleList createLocaleList(java.util.Locale[])
com.google.android.material.textfield.TextInputLayout: int getBoxStrokeColor()
com.google.android.material.chip.Chip: void setChipIconSize(float)
com.google.android.material.chip.Chip: void setAccessibilityClassName(java.lang.CharSequence)
com.google.android.material.button.MaterialButton: void setBackgroundColor(int)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState detachState()
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$LayoutManager getLayoutManager()
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
androidx.appcompat.widget.TooltipCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
androidx.constraintlayout.widget.ConstraintLayout: void setId(int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getMutators()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay[] values()
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
androidx.core.app.NotificationCompatBuilder$Api29Impl: android.app.Notification$Builder setAllowSystemGeneratedContextualActions(android.app.Notification$Builder,boolean)
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean shouldUpdate()
com.google.android.material.internal.ClippableRoundedCornerLayout: float getCornerRadius()
androidx.core.view.ViewCompat$Api21Impl: boolean isImportantForAccessibility(android.view.View)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: AppCompatTextViewAutoSizeHelper$Impl29()
com.google.android.material.chip.Chip: android.content.res.ColorStateList getCloseIconTint()
com.google.android.material.textfield.TextInputLayout: void setBoxBackgroundMode(int)
androidx.core.content.ContextCompat$Api28Impl: java.util.concurrent.Executor getMainExecutor(android.content.Context)
androidx.appcompat.widget.AppCompatTextHelper$Api26Impl: void setAutoSizeTextTypeUniformWithConfiguration(android.widget.TextView,int,int,int,int)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: int getMaxInlineActionWidth()
androidx.core.content.res.ResourcesCompat$Api23Impl: android.content.res.ColorStateList getColorStateList(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
io.flutter.embedding.engine.FlutterJNI: void onSurfaceCreated(android.view.Surface)
com.tncompany.vpn.MainActivity: MainActivity()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
io.flutter.embedding.engine.FlutterJNI: void updateCustomAccessibilityActions(java.nio.ByteBuffer,java.lang.String[])
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityPaneTitle(android.view.View,java.lang.CharSequence)
com.google.android.material.chip.Chip: void setChipCornerRadius(float)
com.google.android.material.textfield.TextInputLayout: void setTypeface(android.graphics.Typeface)
com.google.android.material.internal.TouchObserverFrameLayout: TouchObserverFrameLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.textfield.TextInputLayout: int getEndIconMode()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.Toolbar: android.view.View getNavButtonView()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
com.google.android.material.internal.NavigationMenuItemView: void setActionView(android.view.View)
androidx.appcompat.widget.SearchView: void setIconified(boolean)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getObservatoryUri()
io.flutter.embedding.engine.FlutterJNI: void loadDartDeferredLibrary(int,java.lang.String[])
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
androidx.core.view.ViewParentCompat$Api21Impl: void onStopNestedScroll(android.view.ViewParent,android.view.View)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: long id()
androidx.core.content.ContextCompat$Api33Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.appcompat.widget.SwitchCompat: int getSwitchMinWidth()
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: void requestDartDeferredLibrary(int)
com.google.android.material.chip.Chip: void setCloseIconSizeResource(int)
androidx.constraintlayout.widget.Guideline: void setGuidelineBegin(int)
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setVisibility(android.app.Notification$Builder,int)
com.google.android.material.textfield.TextInputLayout: void setHelperTextColor(android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: boolean nativeGetIsSoftwareRenderingEnabled()
com.google.android.material.chip.Chip: void setChipBackgroundColor(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatEditText: void setSupportBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.carousel.CarouselLayoutManager: CarouselLayoutManager()
com.google.android.material.textfield.TextInputLayout: void setErrorIconTintList(android.content.res.ColorStateList)
androidx.preference.PreferenceGroup: PreferenceGroup(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.widget.TextViewCompat$Api23Impl: android.graphics.PorterDuff$Mode getCompoundDrawableTintMode(android.widget.TextView)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Action$Builder addExtras(android.app.Notification$Action$Builder,android.os.Bundle)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType[] values()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay valueOf(java.lang.String)
androidx.appcompat.widget.DropDownListView$Api33Impl: void setSelectedChildViewEnabled(android.widget.AbsListView,boolean)
com.google.android.material.transformation.ExpandableBehavior: ExpandableBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: int getMaxWidth()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedPreScroll(android.view.ViewParent,android.view.View,int,int,int[])
com.google.android.material.button.MaterialButton: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.transformation.ExpandableBehavior: ExpandableBehavior()
io.flutter.embedding.engine.FlutterJNI: void updateJavaAssetManager(android.content.res.AssetManager,java.lang.String)
androidx.appcompat.widget.AppCompatEditText: java.lang.CharSequence getText()
androidx.appcompat.widget.AppCompatCheckBox: void setBackgroundResource(int)
com.google.android.material.chip.Chip: void setChipStrokeWidth(float)
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOpNoThrow(android.app.AppOpsManager,java.lang.String,java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
androidx.transition.ViewGroupUtils$Api29Impl: int getChildDrawingOrder(android.view.ViewGroup,int)
androidx.appcompat.widget.Toolbar$Api33Impl: void tryUnregisterOnBackInvokedCallback(java.lang.Object,java.lang.Object)
androidx.appcompat.widget.AppCompatCheckBox: void setButtonDrawable(int)
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setBadgeIconType(android.app.Notification$Builder,int)
io.flutter.view.FlutterCallbackInformation: FlutterCallbackInformation(java.lang.String,java.lang.String,java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: android.graphics.Matrix getPlatformViewMatrix()
androidx.coordinatorlayout.widget.CoordinatorLayout: void setStatusBarBackground(android.graphics.drawable.Drawable)
androidx.core.widget.TextViewCompat$Api23Impl: void setHyphenationFrequency(android.widget.TextView,int)
com.google.android.material.textfield.TextInputLayout: void setLengthCounter(com.google.android.material.textfield.TextInputLayout$LengthCounter)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMinimumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
com.google.android.material.internal.NavigationMenuItemView: void setTextColor(android.content.res.ColorStateList)
androidx.appcompat.widget.SearchView: int getPreferredHeight()
com.google.android.material.chip.Chip: void setCloseIconContentDescription(java.lang.CharSequence)
androidx.recyclerview.widget.RecyclerView: void setViewCacheExtension(androidx.recyclerview.widget.RecyclerView$ViewCacheExtension)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
com.google.android.material.chip.Chip: void setOnCheckedChangeListener(android.widget.CompoundButton$OnCheckedChangeListener)
com.google.android.material.textfield.TextInputLayout: void setEndIconContentDescription(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
com.google.android.material.textfield.TextInputLayout: float getBoxCornerRadiusBottomStart()
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder setSortKey(android.app.Notification$Builder,java.lang.String)
com.google.android.material.chip.Chip: android.content.res.ColorStateList getChipStrokeColor()
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getEndIconDrawable()
com.google.android.material.internal.NavigationMenuView: NavigationMenuView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void registerTexture(long,io.flutter.embedding.engine.renderer.SurfaceTextureWrapper)
androidx.appcompat.widget.SwitchCompat: void setSwitchPadding(int)
com.google.android.material.textfield.TextInputLayout: void setCounterOverflowTextColor(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.os.LocaleListCompat$Api24Impl: android.os.LocaleList getDefault()
com.google.android.material.internal.CheckableImageButton: void setPressed(boolean)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Action build(android.app.Notification$Action$Builder)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getStartIconContentDescription()
androidx.constraintlayout.widget.ConstraintLayout: void setMinWidth(int)
androidx.core.widget.CompoundButtonCompat$Api21Impl: android.content.res.ColorStateList getButtonTintList(android.widget.CompoundButton)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedScroll(android.view.View,int,int,int,int,int[])
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getOrCreateOutlinedDropDownMenuBackground()
com.google.android.material.chip.Chip: void setInternalOnCheckedChangeListener(com.google.android.material.internal.MaterialCheckable$OnCheckedChangeListener)
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
androidx.appcompat.widget.SearchView: void setOnQueryTextListener(androidx.appcompat.widget.SearchView$OnQueryTextListener)
androidx.appcompat.widget.SearchView: void setSuggestionsAdapter(androidx.cursoradapter.widget.CursorAdapter)
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getPrefixText()
io.flutter.embedding.engine.FlutterJNI: void init(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeColorStateList(android.content.res.ColorStateList)
androidx.browser.customtabs.CustomTabsIntent$Api23Impl: android.app.ActivityOptions makeBasicActivityOptions()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreFling(android.view.View,float,float)
com.google.android.material.chip.Chip: void setCloseIconResource(int)
androidx.core.view.ViewCompat$Api31Impl: androidx.core.view.ContentInfoCompat performReceiveContent(android.view.View,androidx.core.view.ContentInfoCompat)
androidx.appcompat.widget.AppCompatEditText: android.view.textclassifier.TextClassifier getTextClassifier()
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat$CollectionItemInfoCompat buildCollectionItemInfoCompat(boolean,int,int,int,int,boolean,java.lang.String,java.lang.String)
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
com.google.android.material.textfield.TextInputLayout: float getBoxCornerRadiusTopEnd()
com.google.android.material.textfield.TextInputLayout: com.google.android.material.shape.MaterialShapeDrawable getBoxBackground()
io.flutter.view.TextureRegistry$SurfaceProducer: android.view.Surface getSurface()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getFinalClippingPaths()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getPasswordVisibilityToggleContentDescription()
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
androidx.appcompat.widget.AppCompatButton: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$RecycledViewPool getRecycledViewPool()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
com.google.android.material.sidesheet.SideSheetBehavior: SideSheetBehavior()
androidx.appcompat.widget.SwitchCompat: void setShowText(boolean)
de.blinkt.openvpn.core.NativeUtils: void jniclose(int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: boolean canApplyTheme(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.constraintlayout.widget.Barrier: int getType()
com.google.android.material.textfield.TextInputLayout: void setHintInternal(java.lang.CharSequence)
androidx.core.view.ViewCompat$Api21Impl: boolean startNestedScroll(android.view.View,int)
androidx.appcompat.widget.AppCompatTextView: void setAllCaps(boolean)
com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior: AppBarLayout$ScrollingViewBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: int getStrokeWidth()
io.flutter.embedding.engine.FlutterJNI: void nativeCleanupMessageData(long)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action[] values()
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintList(android.widget.TextView,android.content.res.ColorStateList)
androidx.appcompat.widget.SearchView: int getPreferredWidth()
androidx.constraintlayout.widget.ConstraintLayout: ConstraintLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.transformation.FabTransformationSheetBehavior: FabTransformationSheetBehavior()
androidx.core.view.ViewCompat$Api21Impl: java.lang.String getTransitionName(android.view.View)
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
androidx.profileinstaller.ProfileInstallerInitializer$Choreographer16Impl: void postFrameCallback(java.lang.Runnable)
androidx.appcompat.widget.AppCompatEditText: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action,java.lang.Object)
androidx.core.graphics.drawable.IconCompat$Api28Impl: android.net.Uri getUri(java.lang.Object)
com.google.android.material.search.SearchBar$ScrollingViewBehavior: SearchBar$ScrollingViewBehavior()
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.String permissionToOp(java.lang.String)
com.google.android.material.button.MaterialButton: void setToggleCheckedStateOnClick(boolean)
io.flutter.embedding.android.FlutterSurfaceView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
com.google.android.material.chip.Chip: void setRippleColorResource(int)
androidx.appcompat.widget.Toolbar: void setBackInvokedCallbackEnabled(boolean)
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
com.google.android.material.button.MaterialButtonToggleGroup: void setSingleSelection(int)
androidx.core.view.ViewCompat$Api21Impl: boolean hasNestedScrollingParent(android.view.View)
com.google.android.material.floatingactionbutton.FloatingActionButton$Behavior: FloatingActionButton$Behavior(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface getSurface()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceWindowChanged(long,android.view.Surface)
androidx.appcompat.widget.AppCompatEditText: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.preference.internal.PreferenceImageView: int getMaxHeight()
com.google.android.material.textfield.TextInputLayout: void setExpandedHintEnabled(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
androidx.transition.ViewUtilsApi21$Api29Impl: void transformMatrixToGlobal(android.view.View,android.graphics.Matrix)
androidx.recyclerview.widget.LinearLayoutManager: LinearLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.FlutterEngine getAttachedFlutterEngine()
com.google.android.material.internal.CheckableImageButton: CheckableImageButton(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void disableFenceForTest()
androidx.core.view.animation.PathInterpolatorCompat$Api21Impl: android.view.animation.Interpolator createPathInterpolator(float,float,float,float)
com.tekartik.sqflite.SqflitePlugin: SqflitePlugin()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateJavaAssetManager(long,android.content.res.AssetManager,java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getActiveReader()
com.google.android.material.appbar.AppBarLayout$BaseBehavior: AppBarLayout$BaseBehavior()
kotlin.time.DurationUnit: kotlin.time.DurationUnit valueOf(java.lang.String)
androidx.activity.OnBackPressedDispatcher$Api33Impl: android.window.OnBackInvokedCallback createOnBackInvokedCallback(kotlin.jvm.functions.Function0)
com.google.android.material.appbar.MaterialToolbar: void setSubtitleCentered(boolean)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
androidx.core.widget.EdgeEffectCompat$Api31Impl: float getDistance(android.widget.EdgeEffect)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
com.google.android.material.textfield.TextInputLayout: void setTextInputAccessibilityDelegate(com.google.android.material.textfield.TextInputLayout$AccessibilityDelegate)
com.google.android.material.textfield.TextInputLayout: void setBoxBackgroundColorStateList(android.content.res.ColorStateList)
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
com.google.android.material.textfield.TextInputLayout: void setMaxEms(int)
androidx.appcompat.widget.AppCompatButton: void setFilters(android.text.InputFilter[])
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
androidx.constraintlayout.widget.Barrier: void setType(int)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
androidx.core.view.ViewCompat$Api30Impl: boolean isImportantForContentCapture(android.view.View)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization valueOf(java.lang.String)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$500(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
com.google.android.material.textfield.TextInputLayout: void setBoxBackgroundColorResource(int)
androidx.emoji2.text.ConcurrencyHelpers$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
androidx.appcompat.widget.DropDownListView$Api33Impl: boolean isSelectedChildViewEnabled(android.widget.AbsListView)
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
com.google.android.material.behavior.SwipeDismissBehavior: SwipeDismissBehavior()
androidx.appcompat.widget.SwitchCompat: void setSwitchMinWidth(int)
androidx.appcompat.widget.AppCompatEditText: void setEmojiCompatEnabled(boolean)
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$402(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.View)
androidx.appcompat.widget.AppCompatButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeDestroy(long)
com.google.android.material.chip.Chip: void setChipIconTint(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader29()
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
io.flutter.view.TextureRegistry$SurfaceProducer: long id()
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode[] values()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
io.flutter.embedding.engine.FlutterJNI: void nativeNotifyLowMemoryWarning(long)
androidx.appcompat.widget.ListPopupWindow$Api29Impl: void setEpicenterBounds(android.widget.PopupWindow,android.graphics.Rect)
androidx.core.view.ViewCompat$Api30Impl: androidx.core.view.WindowInsetsControllerCompat getWindowInsetsController(android.view.View)
kotlin.collections.AbstractList: AbstractList()
androidx.constraintlayout.helper.widget.Flow: void setMaxElementsWrap(int)
com.google.android.material.sidesheet.SideSheetBehavior: SideSheetBehavior(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void removeRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledVerticalScrollFactor(android.view.ViewConfiguration)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOff()
com.google.android.material.chip.Chip: void setChipIconSizeResource(int)
androidx.preference.EditTextPreference: EditTextPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
androidx.core.widget.CompoundButtonCompat$Api23Impl: android.graphics.drawable.Drawable getButtonDrawable(android.widget.CompoundButton)
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
androidx.transition.ViewUtilsApi19$Api29Impl: float getTransitionAlpha(android.view.View)
com.google.android.material.button.MaterialButtonToggleGroup: MaterialButtonToggleGroup(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
com.google.android.material.chip.Chip: Chip(android.content.Context,android.util.AttributeSet)
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setLayoutParams(android.view.ViewGroup$LayoutParams)
io.flutter.embedding.engine.FlutterJNI: void dispatchEmptyPlatformMessage(java.lang.String,int)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getHint()
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
io.flutter.embedding.android.FlutterTextureView: void setRenderSurface(android.view.Surface)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.renderer.FlutterRenderer$ViewportMetrics getViewportMetrics()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat valueOf(java.lang.String)
dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin: PackageInfoPlugin()
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: void setInsets(int,androidx.core.graphics.Insets)
androidx.appcompat.widget.SwitchCompat: void setThumbTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeWidthFocused(int)
de.blinkt.openvpn.core.NativeUtils: java.lang.String[] getIfconfig()
androidx.core.os.BuildCompat$Api30Impl: int getExtensionVersion(int)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeTextType()
com.google.android.material.chip.Chip: void setCloseIconStartPadding(float)
com.google.android.material.textfield.TextInputLayout: void setErrorTextAppearance(int)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder[] values()
androidx.loader.app.LoaderManagerImpl$LoaderViewModel: LoaderManagerImpl$LoaderViewModel()
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurfaces()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.plugin.platform.PlatformViewWrapper: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType[] values()
com.google.android.material.textfield.TextInputLayout: float getBoxCornerRadiusBottomEnd()
com.google.android.material.datepicker.MaterialCalendar$CalendarSelector: com.google.android.material.datepicker.MaterialCalendar$CalendarSelector[] values()
androidx.preference.TwoStatePreference: TwoStatePreference(android.content.Context,android.util.AttributeSet)
de.blinkt.openvpn.core.NativeUtils: byte[] rsasign(byte[],int,boolean)
com.google.android.material.chip.Chip: void setChipIconVisible(boolean)
androidx.appcompat.widget.AppCompatEditText: void setTextClassifier(android.view.textclassifier.TextClassifier)
com.google.android.material.chip.Chip: void setChipMinHeightResource(int)
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
com.google.android.material.chip.Chip: com.google.android.material.animation.MotionSpec getShowMotionSpec()
androidx.appcompat.widget.SwitchCompat: float getThumbPosition()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType[] values()
androidx.constraintlayout.widget.ConstraintLayout: int getOptimizationLevel()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean handlesCropAndRotation()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.android.FlutterTextureView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBaseTransientBottomBar(com.google.android.material.snackbar.BaseTransientBottomBar)
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
com.google.android.material.bottomappbar.BottomAppBar$Behavior: BottomAppBar$Behavior()
com.google.android.material.internal.NavigationMenuItemView: void setHorizontalPadding(int)
com.google.android.material.textfield.TextInputLayout: void setSuffixText(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Api18Impl: boolean isInLayout(android.view.View)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
com.google.android.material.chip.Chip: float getChipEndPadding()
io.flutter.embedding.engine.FlutterJNI: void setRefreshRateFPS(float)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getCodeCacheDir(android.content.Context)
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: java.util.ArrayList getCurrentMenuItems()
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.constraintlayout.helper.widget.Flow: void setHorizontalBias(float)
androidx.preference.internal.PreferenceImageView: int getMaxWidth()
de.blinkt.openvpn.core.VpnStatus$LogLevel: de.blinkt.openvpn.core.VpnStatus$LogLevel valueOf(java.lang.String)
androidx.appcompat.widget.SearchView$Api29Impl: void setInputMethodMode(androidx.appcompat.widget.SearchView$SearchAutoComplete,int)
com.google.android.material.chip.Chip: void setChipTextResource(int)
androidx.appcompat.widget.SearchView: void setQueryRefinementEnabled(boolean)
androidx.core.view.ViewGroupCompat$Api21Impl: boolean isTransitionGroup(android.view.ViewGroup)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle getLifecycle()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
io.flutter.embedding.engine.FlutterJNI: void cleanupMessageData(long)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
androidx.appcompat.widget.SwitchCompat: void setTrackTintMode(android.graphics.PorterDuff$Mode)
de.blinkt.openvpn.DisconnectVPNActivity: DisconnectVPNActivity()
androidx.transition.ViewUtilsApi21$Api29Impl: void setAnimationMatrix(android.view.View,android.graphics.Matrix)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: android.view.WindowInsets onProgress(android.view.WindowInsets,java.util.List)
com.google.android.material.internal.NavigationMenuItemView: void setMaxLines(int)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void finalize()
androidx.window.extensions.core.util.function.Consumer: void accept(java.lang.Object)
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader: DynamiteModule$DynamiteLoaderClassLoader()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getChild(android.view.accessibility.AccessibilityNodeInfo,int,int)
com.google.android.material.chip.Chip: float getChipStartPadding()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Api16Impl: int getMaxLines(android.widget.TextView)
androidx.appcompat.widget.SwitchCompat: void setFilters(android.text.InputFilter[])
com.google.android.material.internal.NavigationMenuItemView: void setIconTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
io.flutter.view.AccessibilityViewEmbedder: AccessibilityViewEmbedder(android.view.View,int)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintMode(android.widget.TextView,android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatTextView: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
androidx.appcompat.widget.AppCompatButton: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.android.material.textfield.TextInputLayout: void setHelperText(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
io.flutter.embedding.engine.FlutterJNI: void setDeferredComponentManager(io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager)
androidx.core.view.ViewCompat$Api21Impl: void setTranslationZ(android.view.View,float)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScroll(android.view.ViewParent,android.view.View,int,int,int,int)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetTop(android.view.DisplayCutout)
com.google.android.material.internal.NavigationMenuItemView: void setNeedsEmptyIcon(boolean)
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateDisplayMetrics(long)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
androidx.core.view.ViewGroupCompat$Api21Impl: void setTransitionGroup(android.view.ViewGroup,boolean)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: int getLayoutDirection(android.graphics.drawable.Drawable)
com.google.android.material.internal.CheckableImageButton: void setPressable(boolean)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.appcompat.widget.SearchView: void setQueryHint(java.lang.CharSequence)
androidx.preference.ListPreference: ListPreference(android.content.Context,android.util.AttributeSet)
com.google.android.material.textfield.TextInputEditText: com.google.android.material.textfield.TextInputLayout getTextInputLayout()
androidx.appcompat.widget.AppCompatEditText: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setBackground(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api30Impl: void setStateDescription(android.view.View,java.lang.CharSequence)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$400(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getCheckedIcon()
com.google.android.material.textfield.TextInputLayout: int getCounterMaxLength()
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap getBitmap()
androidx.core.view.ViewCompat$Api26Impl: void addKeyboardNavigationClusters(android.view.View,java.util.Collection,int)
com.google.android.material.chip.Chip: void setMaxWidth(int)
com.google.android.material.textfield.TextInputEditText: TextInputEditText(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.MenuPopupWindow$Api23Impl: void setEnterTransition(android.widget.PopupWindow,android.transition.Transition)
androidx.appcompat.widget.SearchView: void setIconifiedByDefault(boolean)
androidx.core.graphics.drawable.IconCompat$Api30Impl: android.graphics.drawable.Icon createWithAdaptiveBitmapContentUri(android.net.Uri)
androidx.core.app.NotificationCompat$BubbleMetadata$Api29Impl: android.app.Notification$BubbleMetadata toPlatform(androidx.core.app.NotificationCompat$BubbleMetadata)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
androidx.core.view.ViewCompat$Api21Impl: float getTranslationZ(android.view.View)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$OnFlingListener getOnFlingListener()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
com.google.android.material.textfield.TextInputLayout: void setPlaceholderTextAppearance(int)
com.google.android.material.transformation.FabTransformationScrimBehavior: FabTransformationScrimBehavior(android.content.Context,android.util.AttributeSet)
androidx.transition.TransitionUtils$Api28Impl: android.graphics.Bitmap createBitmap(android.graphics.Picture)
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: void setCloseIconVisible(int)
com.google.android.material.button.MaterialButton: void setInsetBottom(int)
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setTimeoutAfter(android.app.Notification$Builder,long)
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getCounterOverflowDescription()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: android.util.DisplayMetrics getRearDisplayMetrics()
androidx.constraintlayout.widget.Barrier: void setAllowsGoneWidget(boolean)
io.flutter.plugins.GeneratedPluginRegistrant: GeneratedPluginRegistrant()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.appcompat.widget.Toolbar$Api33Impl: void tryRegisterOnBackInvokedCallback(java.lang.Object,java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.emoji2.text.EmojiCompatInitializer: EmojiCompatInitializer()
io.flutter.view.TextureRegistry$SurfaceProducer: int getWidth()
com.google.android.material.button.MaterialButton: int getIconPadding()
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
com.google.android.material.chip.Chip: java.lang.CharSequence getCloseIconContentDescription()
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageEmptyResponseCallback(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
androidx.appcompat.widget.DropDownListView$Api21Impl: void drawableHotspotChanged(android.view.View,float,float)
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Api23Impl: android.text.StaticLayout createStaticLayoutForMeasuring(java.lang.CharSequence,android.text.Layout$Alignment,int,int,android.widget.TextView,android.text.TextPaint,androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: ImeSyncDeferringInsetsCallback(android.view.View)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
com.google.android.material.textfield.TextInputLayout: android.graphics.Typeface getTypeface()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$502(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.WindowInsets)
androidx.core.widget.CompoundButtonCompat$Api21Impl: void setButtonTintList(android.widget.CompoundButton,android.content.res.ColorStateList)
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setColor(android.app.Notification$Builder,int)
androidx.core.view.ViewCompat$Api29Impl: void setContentCaptureSession(android.view.View,androidx.core.view.contentcapture.ContentCaptureSessionCompat)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
com.google.android.material.snackbar.SnackbarContentLayout: android.widget.Button getActionView()
com.google.android.material.chip.Chip: android.content.res.ColorStateList getCheckedIconTint()
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Drawable createAdaptiveIconDrawable(android.graphics.drawable.Drawable,android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
kotlinx.coroutines.android.AndroidDispatcherFactory: java.lang.String hintOnError()
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id()
com.google.android.material.internal.CheckableImageButton: void setCheckable(boolean)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType valueOf(java.lang.String)
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
com.google.android.material.button.MaterialButtonToggleGroup: int getVisibleButtonCount()
io.flutter.embedding.engine.FlutterJNI: void setPlatformMessageHandler(io.flutter.embedding.engine.dart.PlatformMessageHandler)
androidx.appcompat.widget.SwitchCompat: void setSwitchTypeface(android.graphics.Typeface)
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Builder setCustomBigContentView(android.app.Notification$Builder,android.widget.RemoteViews)
androidx.appcompat.widget.AppCompatButton: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
com.google.android.material.textfield.TextInputEditText: java.lang.CharSequence getHintFromLayout()
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness valueOf(java.lang.String)
com.google.android.material.textfield.TextInputLayout: void setHintEnabled(boolean)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
androidx.core.view.ViewCompat$Api28Impl: void setAutofillId(android.view.View,androidx.core.view.autofill.AutofillIdCompat)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: android.view.accessibility.AccessibilityNodeInfo$AccessibilityAction getActionScrollInDirection()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsVariationSelector(int)
com.google.android.material.chip.Chip: void setCloseIconVisible(boolean)
androidx.core.view.ViewCompat$Api26Impl: android.view.autofill.AutofillId getAutofillId(android.view.View)
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
com.google.android.material.textfield.TextInputLayout: void setErrorIconDrawable(int)
androidx.constraintlayout.solver.widgets.analyzer.DependencyNode$Type: androidx.constraintlayout.solver.widgets.analyzer.DependencyNode$Type valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
androidx.core.app.RemoteInput$Api26Impl: java.util.Map getDataResultsFromIntent(android.content.Intent,java.lang.String)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature[] values()
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
androidx.preference.SeekBarPreference: SeekBarPreference(android.content.Context,android.util.AttributeSet)
androidx.core.widget.TextViewCompat$Api23Impl: int getHyphenationFrequency(android.widget.TextView)
androidx.appcompat.widget.AppCompatCheckBox: void setAllCaps(boolean)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
de.blinkt.openvpn.core.OpenVPNService: OpenVPNService()
com.google.android.material.button.MaterialButton: void setChecked(boolean)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
com.google.android.material.chip.Chip: void setEllipsize(android.text.TextUtils$TruncateAt)
androidx.core.view.ViewCompat$Api26Impl: void setAutofillHints(android.view.View,java.lang.String[])
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setUniqueId(android.view.accessibility.AccessibilityNodeInfo,java.lang.String)
androidx.core.os.LocaleListCompat$Api24Impl: android.os.LocaleList getAdjustedDefault()
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
com.google.android.material.textfield.TextInputLayout: int getErrorAccessibilityLiveRegion()
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
androidx.preference.PreferenceCategory: PreferenceCategory(android.content.Context,android.util.AttributeSet)
androidx.tracing.TraceApi29Impl: boolean isEnabled()
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
androidx.appcompat.widget.SwitchCompat: void setEmojiCompatEnabled(boolean)
com.google.android.material.search.SearchView$Behavior: SearchView$Behavior()
androidx.core.content.ContextCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.Context,int)
androidx.constraintlayout.helper.widget.Flow: void setVerticalGap(int)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintMode(android.view.MenuItem,android.graphics.PorterDuff$Mode)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
com.google.android.material.bottomsheet.BottomSheetBehavior: BottomSheetBehavior()
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType[] values()
com.google.android.material.chip.Chip: void setTextStartPaddingResource(int)
com.google.android.material.button.MaterialButton: int getInsetBottom()
io.flutter.embedding.engine.FlutterJNI: void deferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType valueOf(java.lang.String)
de.blinkt.openvpn.core.Connection$ProxyType: de.blinkt.openvpn.core.Connection$ProxyType valueOf(java.lang.String)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
androidx.core.app.NotificationCompat$BubbleMetadata$Api30Impl: android.app.Notification$BubbleMetadata toPlatform(androidx.core.app.NotificationCompat$BubbleMetadata)
com.google.android.material.textfield.TextInputLayout: void setHelperTextEnabled(boolean)
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact valueOf(java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintList(android.view.MenuItem,android.content.res.ColorStateList)
androidx.appcompat.widget.SearchView: void setOnSearchClickListener(android.view.View$OnClickListener)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setFitsSystemWindows(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
com.google.android.material.chip.Chip: void setIconStartPaddingResource(int)
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportButtonTintMode()
androidx.core.view.ViewCompat$Api26Impl: void setFocusedByDefault(android.view.View,boolean)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader access$800(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode[] values()
androidx.appcompat.widget.SwitchCompat: SwitchCompat(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: void setCloseIconTint(android.content.res.ColorStateList)
androidx.core.widget.EdgeEffectCompat$Api31Impl: android.widget.EdgeEffect create(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: HiddenLifecycleReference(androidx.lifecycle.Lifecycle)
com.google.android.material.textfield.TextInputLayout: void setErrorTextColor(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void asyncWaitForVsync(long)
com.google.android.material.chip.Chip: void setCloseIcon(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void nativeDeferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.preference.internal.PreferenceImageView: void setMaxWidth(int)
com.google.android.material.floatingactionbutton.FloatingActionButton$Behavior: FloatingActionButton$Behavior()
com.google.android.material.chip.Chip: void setChipBackgroundColorResource(int)
com.google.android.material.textfield.TextInputLayout: void setErrorEnabled(boolean)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.net.Uri getUri(java.lang.Object)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void pruneImageReaderQueue()
com.google.android.material.button.MaterialButtonToggleGroup: java.util.List getCheckedButtonIds()
androidx.appcompat.widget.AppCompatTextHelper$Api17Impl: void setCompoundDrawablesRelativeWithIntrinsicBounds(android.widget.TextView,android.graphics.drawable.Drawable,android.graphics.drawable.Drawable,android.graphics.drawable.Drawable,android.graphics.drawable.Drawable)
io.flutter.view.TextureRegistry$SurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getPrefixTextColor()
androidx.core.view.ViewCompat$Api21Impl: void setZ(android.view.View,float)
com.google.android.gms.common.api.internal.LifecycleCallback: com.google.android.gms.common.api.internal.LifecycleFragment getChimeraLifecycleFragmentImpl(com.google.android.gms.common.api.internal.LifecycleActivity)
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: void setTextOnInternal(java.lang.CharSequence)
com.google.android.material.datepicker.MaterialCalendar$CalendarSelector: com.google.android.material.datepicker.MaterialCalendar$CalendarSelector valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void scheduleFrame()
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabledInNative(boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceCreated(long,android.view.Surface)
androidx.appcompat.widget.ListPopupWindow$Api29Impl: void setIsClippedToScreen(android.widget.PopupWindow,boolean)
com.google.android.material.chip.Chip: void setCheckable(boolean)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
androidx.core.content.ContextCompat$Api26Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType valueOf(java.lang.String)
com.google.android.material.textfield.TextInputLayout: void setMinWidth(int)
androidx.appcompat.widget.SearchView: int getImeOptions()
androidx.transition.ViewUtilsApi21$Api29Impl: void transformMatrixToLocal(android.view.View,android.graphics.Matrix)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setHelperTextTextAppearance(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getOrCreatePerImageReader(android.media.ImageReader)
androidx.constraintlayout.widget.ConstraintHelper: int[] getReferencedIds()
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleContentDescription(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateRefreshRate(float)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: FlutterMutatorsStack()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifierBase(int)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getIconTint()
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
androidx.recyclerview.widget.RecyclerView: long getNanoTime()
com.google.android.material.appbar.MaterialToolbar: MaterialToolbar(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatReceiveContentHelper$OnDropApi24Impl: boolean onDropForTextView(android.view.DragEvent,android.widget.TextView,android.app.Activity)
androidx.recyclerview.widget.RecyclerView: void setPreserveFocusAfterLayout(boolean)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
androidx.core.content.res.ResourcesCompat$Api23Impl: int getColor(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.appcompat.widget.AppCompatButton: void setBackgroundResource(int)
androidx.appcompat.widget.SwitchCompat: void setThumbTextPadding(int)
androidx.recyclerview.widget.RecyclerView: int getMinFlingVelocity()
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: android.content.Context getPresentationContext()
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintMode(android.view.View,android.graphics.PorterDuff$Mode)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMaximumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintMode(android.widget.ImageView,android.graphics.PorterDuff$Mode)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides[] values()
com.google.android.material.textfield.TextInputLayout: com.google.android.material.textfield.TextInputLayout$LengthCounter getLengthCounter()
io.flutter.embedding.android.FlutterImageView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnTrimMemoryListener(io.flutter.view.TextureRegistry$OnTrimMemoryListener)
de.blinkt.openvpn.core.ConfigParser$linestate: de.blinkt.openvpn.core.ConfigParser$linestate valueOf(java.lang.String)
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
androidx.core.app.NotificationCompatBuilder$Api23Impl: android.app.Notification$Builder setLargeIcon(android.app.Notification$Builder,android.graphics.drawable.Icon)
androidx.profileinstaller.ProfileInstallerInitializer$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
androidx.core.view.ViewCompat$Api31Impl: java.lang.String[] getReceiveContentMimeTypes(android.view.View)
io.flutter.view.TextureRegistry$ImageTextureEntry: long id()
androidx.recyclerview.widget.RecyclerView: void setRecycledViewPool(androidx.recyclerview.widget.RecyclerView$RecycledViewPool)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetRight(android.view.DisplayCutout)
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getThumbTintMode()
androidx.recyclerview.widget.RecyclerView: java.lang.CharSequence getAccessibilityClassName()
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmoji(int)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getRippleColor()
com.google.android.material.button.MaterialButtonToggleGroup: void setEnabled(boolean)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
androidx.appcompat.widget.SwitchCompat: void setThumbDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
com.google.android.material.internal.NavigationMenuItemView: void setCheckable(boolean)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspot(android.graphics.drawable.Drawable,float,float)
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
androidx.appcompat.widget.SwitchCompat: int getSwitchPadding()
androidx.appcompat.widget.AppCompatButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean isAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo)
androidx.activity.OnBackPressedDispatcher$Api34Impl: android.window.OnBackInvokedCallback createOnBackAnimationCallback(kotlin.jvm.functions.Function1,kotlin.jvm.functions.Function1,kotlin.jvm.functions.Function0,kotlin.jvm.functions.Function0)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getCounterOverflowTextColor()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
androidx.appcompat.widget.AppCompatEditText: androidx.appcompat.widget.AppCompatEditText$SuperCaller getSuperCaller()
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
androidx.constraintlayout.helper.widget.Flow: void setFirstVerticalBias(float)
io.flutter.embedding.engine.FlutterJNI: void addEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: int getErrorCurrentTextColors()
androidx.lifecycle.ProcessLifecycleOwner$Api29Impl: void registerActivityLifecycleCallbacks(android.app.Activity,android.app.Application$ActivityLifecycleCallbacks)
com.google.android.material.textfield.TextInputLayout: void setShapeAppearanceModel(com.google.android.material.shape.ShapeAppearanceModel)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$102(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getDefaultHintTextColor()
io.flutter.view.AccessibilityViewEmbedder: android.view.View platformViewOfNode(int)
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
androidx.browser.customtabs.CustomTabsIntent$Api34Impl: void setShareIdentityEnabled(android.app.ActivityOptions,boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onTrimMemory(int)
androidx.appcompat.widget.AppCompatEditText: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeaturesInNative(int)
androidx.appcompat.widget.Toolbar: void setLogo(int)
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getErrorIconDrawable()
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29(androidx.core.view.WindowInsetsCompat)
com.google.android.material.button.MaterialButton: com.google.android.material.shape.ShapeAppearanceModel getShapeAppearanceModel()
androidx.core.app.RemoteInput$Api26Impl: android.app.RemoteInput$Builder setAllowDataType(android.app.RemoteInput$Builder,java.lang.String,boolean)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
io.flutter.embedding.engine.FlutterJNI: FlutterJNI()
io.flutter.embedding.engine.FlutterJNI: void onPreEngineRestart()
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeWidthResource(int)
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
androidx.appcompat.resources.Compatibility$Api21Impl: android.graphics.drawable.Drawable createFromXmlInner(android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.core.widget.NestedScrollView: int getScrollRange()
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation valueOf(java.lang.String)
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
io.flutter.embedding.engine.FlutterJNI: boolean getIsSoftwareRenderingEnabled()
com.google.android.material.textfield.TextInputLayout: void setErrorIconOnClickListener(android.view.View$OnClickListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Builder setCustomContentView(android.app.Notification$Builder,android.widget.RemoteViews)
androidx.core.view.ViewCompat$Api26Impl: android.view.View keyboardNavigationClusterSearch(android.view.View,android.view.View,int)
com.google.android.material.button.MaterialButton: void setCheckable(boolean)
com.google.android.material.chip.Chip: float getCloseIconSize()
com.google.android.material.chip.Chip: android.content.res.ColorStateList getRippleColor()
io.flutter.embedding.engine.FlutterJNI: void removeEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
com.google.android.material.button.MaterialButton: void setIconGravity(int)
io.flutter.embedding.engine.FlutterJNI: java.lang.String[] computePlatformResolvedLocale(java.lang.String[])
com.google.android.material.chip.Chip: void setCheckedIconResource(int)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode valueOf(java.lang.String)
com.google.android.material.textfield.TextInputLayout: void setMinEms(int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float onPullDistance(android.widget.EdgeEffect,float,float)
androidx.appcompat.widget.SwitchCompat: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
androidx.recyclerview.widget.RecyclerView: void setEdgeEffectFactory(androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory)
androidx.transition.FragmentTransitionSupport: FragmentTransitionSupport()
com.google.android.material.chip.Chip: void setCloseIconTintResource(int)
io.flutter.embedding.engine.FlutterJNI: void runBundleAndSnapshotFromLibrary(java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setLayoutParams(android.view.ViewGroup$LayoutParams)
com.google.android.material.chip.Chip: void setCheckableResource(int)
com.google.android.material.appbar.MaterialToolbar: void setNavigationIconTint(int)
com.google.android.material.chip.Chip: void setShowMotionSpecResource(int)
androidx.fragment.app.DefaultSpecialEffectsController$Api26Impl: void setCurrentPlayTime(android.animation.AnimatorSet,long)
com.google.android.material.textfield.TextInputLayout: void setEndIconTintList(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api26Impl: void setImportantForAutofill(android.view.View,int)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeErrorColor(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatTextHelper$Api26Impl: int getAutoSizeStepGranularity(android.widget.TextView)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
io.flutter.view.TextureRegistry$SurfaceProducer: int getHeight()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState[] values()
com.google.android.material.chip.Chip: float getIconEndPadding()
com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior: AppBarLayout$ScrollingViewBehavior()
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Action$Builder addRemoteInput(android.app.Notification$Action$Builder,android.app.RemoteInput)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View$OnApplyWindowInsetsListener getInsetsListener()
androidx.appcompat.widget.AppCompatEditText: android.text.Editable getText()
com.google.android.material.button.MaterialButtonToggleGroup: int getCheckedButtonId()
com.google.android.material.chip.Chip: float getCloseIconStartPadding()
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: boolean isAttached()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifier(int)
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
com.google.android.material.appbar.AppBarLayout$BaseBehavior: AppBarLayout$BaseBehavior(android.content.Context,android.util.AttributeSet)
androidx.core.widget.TextViewCompat$Api23Impl: android.content.res.ColorStateList getCompoundDrawableTintList(android.widget.TextView)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setEmojiCompatEnabled(boolean)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context)
com.google.android.material.textfield.TextInputLayout: void setStartIconDrawable(android.graphics.drawable.Drawable)
androidx.core.content.ContextCompat$Api23Impl: java.lang.String getSystemServiceName(android.content.Context,java.lang.Class)
androidx.transition.ViewUtilsApi19$Api29Impl: void setTransitionAlpha(android.view.View,float)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointRegionalIndicator(int)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getPlaceholderText()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getType(java.lang.Object)
androidx.core.view.ViewCompat$Api28Impl: void removeOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
androidx.core.app.RemoteInput$Api29Impl: int getEditChoicesBeforeSending(java.lang.Object)
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetWidth()
io.flutter.embedding.engine.FlutterJNI: boolean ShouldDisableAHB()
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
com.google.android.material.textfield.TextInputLayout: void setEndIconActivated(boolean)
com.google.android.material.button.MaterialButton: int getCornerRadius()
io.flutter.embedding.engine.FlutterJNI: void nativeSetViewportMetrics(long,float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
androidx.fragment.app.DefaultSpecialEffectsController$Api26Impl: void reverse(android.animation.AnimatorSet)
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.widget.TextViewCompat$Api28Impl: void setFirstBaselineToTopHeight(android.widget.TextView,int)
com.google.android.material.textfield.TextInputLayout: void setPlaceholderText(java.lang.CharSequence)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onStartNestedScroll(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.core.view.ViewCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness[] values()
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getChipIcon()
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
androidx.appcompat.widget.AppCompatCheckBox: void setButtonDrawable(android.graphics.drawable.Drawable)
androidx.core.app.NotificationCompatBuilder$Api26Impl: android.app.Notification$Builder setColorized(android.app.Notification$Builder,boolean)
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact[] values()
com.google.android.material.textfield.TextInputLayout: void setBoxCornerFamily(int)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeatures(int)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
androidx.core.app.NotificationCompatBuilder$Api23Impl: android.app.Notification$Action$Builder createBuilder(android.graphics.drawable.Icon,java.lang.CharSequence,android.app.PendingIntent)
androidx.recyclerview.widget.RecyclerView: void setItemAnimator(androidx.recyclerview.widget.RecyclerView$ItemAnimator)
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Builder setRemoteInputHistory(android.app.Notification$Builder,java.lang.CharSequence[])
io.flutter.embedding.android.FlutterImageView: android.view.Surface getSurface()
io.flutter.view.TextureRegistry$ImageTextureEntry: void release()
com.google.android.material.chip.Chip: void setTextAppearance(int)
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.constraintlayout.helper.widget.Flow: void setPadding(int)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Icon createWithAdaptiveBitmap(android.graphics.Bitmap)
androidx.appcompat.widget.ListPopupWindow$Api24Impl: int getMaxAvailableHeight(android.widget.PopupWindow,android.view.View,int,boolean)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getHeight()
io.flutter.embedding.android.FlutterView: android.view.accessibility.AccessibilityNodeProvider getAccessibilityNodeProvider()
io.flutter.view.TextureRegistry$GLTextureConsumer: android.graphics.SurfaceTexture getSurfaceTexture()
androidx.constraintlayout.widget.ConstraintLayout: int getMaxWidth()
androidx.preference.SwitchPreferenceCompat: SwitchPreferenceCompat(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
androidx.core.app.NotificationCompatBuilder$Api21Impl: android.app.Notification$Builder setPublicVersion(android.app.Notification$Builder,android.app.Notification)
com.google.android.material.button.MaterialButtonToggleGroup: int getLastVisibleChildIndex()
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getTooltipText(android.view.MenuItem)
androidx.core.app.NotificationCompatBuilder$Api29Impl: android.app.Notification$Builder setLocusId(android.app.Notification$Builder,java.lang.Object)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.core.app.NotificationCompatBuilder$Api20Impl: android.app.Notification$Builder setLocalOnly(android.app.Notification$Builder,boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void releaseInternal()
androidx.fragment.app.FragmentContainerView: void setDrawDisappearingViewsLast(boolean)
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setContainerTitle(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
com.google.android.material.textfield.TextInputLayout: void setStartIconVisible(boolean)
androidx.recyclerview.widget.StaggeredGridLayoutManager: StaggeredGridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.chip.Chip: void setCheckedIconTintResource(int)
com.google.android.material.textfield.TextInputLayout: void setStartIconContentDescription(int)
com.google.android.material.textfield.TextInputLayout: void setPlaceholderTextColor(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatTextHelper$Api26Impl: void setAutoSizeTextTypeUniformWithPresetSizes(android.widget.TextView,int[],int)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceDestroyed(long)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
com.google.android.material.chip.Chip: android.content.res.ColorStateList getChipBackgroundColor()
com.google.android.material.textfield.TextInputLayout: void setErrorIconOnLongClickListener(android.view.View$OnLongClickListener)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityHeading(android.view.View,boolean)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void markDirty()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$900(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.core.view.ViewCompat$Api23Impl: int getScrollIndicators(android.view.View)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeWidthFocusedResource(int)
com.google.android.material.textfield.TextInputLayout: void setPrefixTextAppearance(int)
com.google.android.material.textfield.TextInputLayout: void setPrefixTextColor(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api28Impl: void addOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnFrameConsumedListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setNumericShortcut(android.view.MenuItem,char,int)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.view.Surface getSurface()
androidx.appcompat.widget.MenuPopupWindow$Api23Impl: void setExitTransition(android.widget.PopupWindow,android.transition.Transition)
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
com.google.android.material.chip.Chip: void setLayoutDirection(int)
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getCounterTextColor()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
androidx.appcompat.widget.AppCompatTextView: androidx.appcompat.widget.AppCompatTextView$SuperCaller getSuperCaller()
io.flutter.embedding.engine.FlutterJNI: void setViewportMetrics(float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
androidx.fragment.app.FragmentContainerView: androidx.fragment.app.Fragment getFragment()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.recyclerview.widget.RecyclerView: void setScrollingTouchSlop(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setAnimationMode(int)
androidx.appcompat.widget.AppCompatEditText: void setBackgroundResource(int)
androidx.fragment.app.FragmentContainerView: void setOnApplyWindowInsetsListener(android.view.View$OnApplyWindowInsetsListener)
com.google.android.material.chip.Chip: void setCloseIconHovered(boolean)
androidx.appcompat.widget.AppCompatImageButton: void setImageLevel(int)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: AppCompatTextViewAutoSizeHelper$Impl()
com.google.android.material.appbar.AppBarLayout$Behavior: AppBarLayout$Behavior()
com.google.android.material.chip.Chip: void setChipIconResource(int)
androidx.appcompat.widget.SearchView: void setSearchableInfo(android.app.SearchableInfo)
androidx.core.widget.PopupWindowCompat$Api23Impl: void setOverlapAnchor(android.widget.PopupWindow,boolean)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.recyclerview.widget.RecyclerView: void setRecyclerListener(androidx.recyclerview.widget.RecyclerView$RecyclerListener)
com.google.android.material.textfield.TextInputLayout: void setEndIconMode(int)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView$Api17Impl: int getLayoutDirection(android.content.res.Configuration)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityDelegate(io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate)
androidx.constraintlayout.helper.widget.Flow: void setFirstVerticalStyle(int)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.core.content.ContextCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
de.blinkt.openvpn.core.OpenVPNManagement$pauseReason: de.blinkt.openvpn.core.OpenVPNManagement$pauseReason[] values()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: android.graphics.SurfaceTexture surfaceTexture()
androidx.core.app.NotificationCompatBuilder$Api24Impl: android.app.Notification$Builder setCustomHeadsUpContentView(android.app.Notification$Builder,android.widget.RemoteViews)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory[] values()
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
androidx.appcompat.widget.AppCompatTextHelper$Api17Impl: void setTextLocale(android.widget.TextView,java.util.Locale)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageEmptyResponseCallback(long,int)
com.google.android.material.textfield.TextInputLayout: void setCursorErrorColor(android.content.res.ColorStateList)
androidx.appcompat.widget.SwitchCompat: void setThumbTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void updateRefreshRate()
com.google.android.material.textfield.TextInputLayout: int getBoxBackgroundMode()
androidx.core.view.ViewCompat$Api26Impl: void setNextClusterForwardId(android.view.View,int)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
com.google.android.material.appbar.AppBarLayout$Behavior: AppBarLayout$Behavior(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api21Impl: float getZ(android.view.View)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setStatusBarBackgroundResource(int)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessage(java.lang.String,java.nio.ByteBuffer,int,long)
androidx.core.view.ViewCompat$Api26Impl: boolean restoreDefaultFocus(android.view.View)
com.google.android.material.textfield.TextInputLayout: android.widget.ImageView$ScaleType getEndIconScaleType()
com.google.android.material.chip.Chip: void setTextStartPadding(float)
com.google.android.material.bottomappbar.BottomAppBar$Behavior: BottomAppBar$Behavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: boolean getTargetCheckedState()
com.google.android.material.appbar.MaterialToolbar: java.lang.Integer getNavigationIconTint()
androidx.appcompat.resources.Compatibility$Api18Impl: void setAutoCancel(android.animation.ObjectAnimator,boolean)
androidx.appcompat.widget.AppCompatTextHelper$Api24Impl: android.os.LocaleList forLanguageTags(java.lang.String)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getHelperText()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
io.flutter.embedding.android.FlutterImageView: android.media.ImageReader getImageReader()
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind valueOf(java.lang.String)
io.flutter.plugin.platform.PlatformViewWrapper: void setLayoutParams(android.widget.FrameLayout$LayoutParams)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.google.android.material.textfield.TextInputLayout: void setCounterOverflowTextAppearance(int)
androidx.core.app.RemoteInput$Api20Impl: void addResultsToIntent(java.lang.Object,android.content.Intent,android.os.Bundle)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType valueOf(java.lang.String)
com.google.android.material.chip.Chip: float getIconStartPadding()
com.google.android.material.datepicker.MaterialCalendarGridView: MaterialCalendarGridView(android.content.Context,android.util.AttributeSet)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Drawable loadDrawable(android.graphics.drawable.Icon,android.content.Context)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.accessibility.AccessibilityNodeInfo)
com.google.android.material.bottomsheet.BottomSheetBehavior: BottomSheetBehavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
androidx.constraintlayout.widget.ConstraintLayout: int getPaddingWidth()
com.google.android.material.button.MaterialButton: void setOnPressedChangeListenerInternal(com.google.android.material.button.MaterialButton$OnPressedChangeListener)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getCursorErrorColor()
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
com.google.android.material.button.MaterialButton: void setBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setHintTextColor(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api29Impl: void setSystemGestureExclusionRects(android.view.View,java.util.List)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
androidx.appcompat.widget.SwitchCompat: void setAllCaps(boolean)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory valueOf(java.lang.String)
com.google.android.material.button.MaterialButton: void setTextAlignment(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.Image acquireLatestImage()
kotlinx.coroutines.flow.SharingCommand: kotlinx.coroutines.flow.SharingCommand valueOf(java.lang.String)
androidx.fragment.app.FragmentContainerView: FragmentContainerView(android.content.Context,android.util.AttributeSet)
de.blinkt.openvpn.core.ConnectionStatus: de.blinkt.openvpn.core.ConnectionStatus valueOf(java.lang.String)
androidx.coordinatorlayout.widget.CoordinatorLayout: CoordinatorLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.appcompat.widget.AppCompatButton: void setAllCaps(boolean)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetBottom(android.view.DisplayCutout)
androidx.core.view.ViewCompat$Api21Impl: void setTransitionName(android.view.View,java.lang.String)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
com.google.android.material.chip.Chip: void setMinLines(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void waitOnFence(android.media.Image)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType[] values()
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleDrawable(int)
com.google.android.material.textfield.TextInputLayout: void setStartIconTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setTextSelectable(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
androidx.appcompat.widget.SwitchCompat: boolean getShowText()
androidx.recyclerview.widget.RecyclerView: void suppressLayout(boolean)
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getBackgroundDrawable()
androidx.appcompat.widget.AppCompatButton: int getAutoSizeStepGranularity()
com.google.android.material.chip.Chip: void setChipStartPaddingResource(int)
androidx.core.os.ConfigurationCompat$Api24Impl: void setLocales(android.content.res.Configuration,androidx.core.os.LocaleListCompat)
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar$Api33Impl: android.window.OnBackInvokedDispatcher findOnBackInvokedDispatcher(android.view.View)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setContentDescription(android.view.MenuItem,java.lang.CharSequence)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode valueOf(java.lang.String)
com.google.android.material.chip.Chip: void setShowMotionSpec(com.google.android.material.animation.MotionSpec)
io.flutter.embedding.engine.FlutterJNI: void nativeUnregisterTexture(long,long)
io.flutter.embedding.engine.FlutterJNI: void notifyLowMemoryWarning()
com.google.android.material.internal.NavigationMenuItemView: void setChecked(boolean)
com.google.android.material.button.MaterialButton: android.graphics.drawable.Drawable getIcon()
com.google.android.material.textfield.TextInputLayout: void setStartIconScaleType(android.widget.ImageView$ScaleType)
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
androidx.lifecycle.ReportFragment: ReportFragment()
com.google.android.material.appbar.MaterialToolbar: void setLogoScaleType(android.widget.ImageView$ScaleType)
com.google.android.material.button.MaterialButton: void setIconTintMode(android.graphics.PorterDuff$Mode)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTint(android.graphics.drawable.Drawable,int)
androidx.core.widget.ImageViewCompat$Api21Impl: android.content.res.ColorStateList getImageTintList(android.widget.ImageView)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getSuffixText()
com.google.android.material.button.MaterialButtonToggleGroup: void setSingleSelection(boolean)
androidx.window.layout.util.ContextCompatHelperApi30: androidx.core.view.WindowInsetsCompat currentWindowInsets(android.content.Context)
com.google.android.material.internal.NavigationMenuView: int getWindowAnimations()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
androidx.constraintlayout.widget.ConstraintLayout: void setMinHeight(int)
