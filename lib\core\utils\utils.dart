import 'dart:math';

import 'package:flutter/services.dart';

export 'navigations.dart';

Future<bool> assetExists(String path) async {
  try {
    await rootBundle.load(path);
    return true;
  } catch (e) {
    return false;
  }
}

double doubleParse(value) {
  return double.tryParse(value.toString()) ?? 0.0;
}

String formatBytes(int bytes, int decimals) {
  if (bytes <= 0) return "0 B";
  const suffixes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  var i = (log(bytes) / log(1024)).floor();
  return '${(bytes / pow(1024, i)).toStringAsFixed(decimals)} ${suffixes[i]}';
}

String paramsToString(Map<String, String>? params) {
  if (params == null) return "";
  String output = "?";
  params.forEach((key, value) {
    output += "$key=$value&";
  });
  return output.substring(0, output.length - 1);
}
