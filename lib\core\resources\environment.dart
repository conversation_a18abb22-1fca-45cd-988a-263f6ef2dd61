import 'package:flutter/material.dart';

ThemeMode themeMode = ThemeMode.system;
bool allowUserChangeTheme = true;

List<Locale> supportedLocales = const [
  Locale('en', ''),
  Locale('vi', ''),
];

const String appName = "TN Vpn";
const String website = "https://vpntn.net/";
const String endpoint = "https://proxytn.com/api/";

const String providerBundleIdentifier = "com.tncompany.vpn.VPNExtension";
const String groupIdentifier = "group.com.tncompany.vpn";
const String iosAppID = "**********";
const String localizationDescription =
    "TN Vpn is a mobile app to help hiding real IP";

const Map<String, int> typeVpn = {
  "Free": 0,
  "Vip": 1,
};
